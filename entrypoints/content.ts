export default defineContentScript({
  matches: ['*://*.zuoyebang.cc/*', '*://*.suanshubang.cc/*'],
  main() {
    console.log('fwyy-tools content script loaded');

    // 初始化XUID内容脚本
    new XuidContentScript();
  },
});

/**
 * XUID切换助手 - 内容脚本 (TypeScript版本)
 * 负责页面级别的XUID检测和通信
 * 排除方舟课程相关功能
 */
class XuidContentScript {
  private currentXuid: string = '';
  private domain: string = '';

  constructor() {
    this.init();
  }

  /**
   * 初始化内容脚本
   */
  private init(): void {
    // 获取当前域名
    this.domain = window.location.hostname;

    // 获取当前XUID
    this.getCurrentXuid();

    // 监听来自后台脚本的消息
    this.listenForMessages();

    // 监听Cookie变化
    this.observeCookieChanges();

    console.log('XUID切换助手内容脚本已加载');
  }

  /**
   * 获取当前页面的XUID
   */
  private getCurrentXuid(): string {
    // 从Cookie中获取XUID
    const cookies = document.cookie.split(';');
    let xuid = '';

    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'XUID') {
        xuid = value;
        break;
      }
    }

    this.currentXuid = xuid;
    return xuid;
  }

  /**
   * 监听来自后台脚本的消息
   */
  private listenForMessages(): void {
    browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
      switch (request.action) {
        case 'getCurrentXuid':
          sendResponse({
            success: true,
            xuid: this.getCurrentXuid(),
            domain: this.domain
          });
          return true; // 表示会同步处理消息

        case 'xuidChanged':
          this.handleXuidChanged(request);
          return true; // 表示会同步处理消息

        default:
          console.log('内容脚本收到未知消息:', request);
          return false; // 表示不处理此消息，让其他监听器处理
      }
    });
  }

  /**
   * 处理XUID变化事件
   */
  private handleXuidChanged(request: any): void {
    console.log('XUID已变化:', request);

    // 更新当前XUID
    this.currentXuid = request.xuid;

    // 显示切换成功的提示
    this.showXuidChangeNotification(request.xuid);
  }

  /**
   * 显示XUID切换通知
   */
  private showXuidChangeNotification(xuid: string): void {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: #4CAF50;
      color: white;
      padding: 12px 20px;
      border-radius: 6px;
      font-family: Arial, sans-serif;
      font-size: 14px;
      z-index: 10000;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      animation: slideInRight 0.3s ease-out;
    `;

    notification.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <span>🔄</span>
        <span>XUID已切换: ${xuid}</span>
      </div>
    `;

    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
      @keyframes slideInRight {
        from {
          transform: translateX(100%);
          opacity: 0;
        }
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }
      @keyframes slideOutRight {
        from {
          transform: translateX(0);
          opacity: 1;
        }
        to {
          transform: translateX(100%);
          opacity: 0;
        }
      }
    `;

    document.head.appendChild(style);
    document.body.appendChild(notification);

    // 3秒后移除通知
    setTimeout(() => {
      notification.style.animation = 'slideOutRight 0.3s ease-out';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
        if (style.parentNode) {
          style.parentNode.removeChild(style);
        }
      }, 300);
    }, 3000);
  }

  /**
   * 观察Cookie变化
   */
  private observeCookieChanges(): void {
    // 定期检查Cookie变化
    let lastXuid = this.currentXuid;

    setInterval(() => {
      const currentXuid = this.getCurrentXuid();
      if (currentXuid !== lastXuid) {
        console.log('检测到XUID变化:', lastXuid, '->', currentXuid);
        lastXuid = currentXuid;
        this.currentXuid = currentXuid;

        // 通知后台脚本
        browser.runtime.sendMessage({
          action: 'xuidDetected',
          xuid: currentXuid,
          domain: this.domain
        }).catch((error) => {
          console.error('发送XUID检测消息失败:', error);
        });
      }
    }, 1000); // 每秒检查一次
  }

  /**
   * 向后台脚本发送消息
   */
  private sendMessageToBackground(message: any): Promise<any> {
    return new Promise((resolve, reject) => {
      browser.runtime.sendMessage(message).then((response) => {
        resolve(response);
      }).catch((error) => {
        reject(error);
      });
    });
  }

  /**
   * 获取所有XUID相关的Cookie
   */
  private getAllXuidCookies(): Array<{ name: string; value: string }> {
    const xuidCookieNames = ['XUID', 'xuid', 'Xuid', 'XUid', 'xuId', 'xUid', 'xUId'];
    const xuidCookies: Array<{ name: string; value: string }> = [];

    const cookies = document.cookie.split(';');

    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (xuidCookieNames.includes(name) && value) {
        xuidCookies.push({ name, value });
      }
    }

    return xuidCookies;
  }

  /**
   * 检查当前页面是否支持XUID功能
   */
  private isDomainSupported(): boolean {
    return this.domain.includes('zuoyebang.cc') || this.domain.includes('suanshubang.cc');
  }

  /**
   * 获取页面中的用户信息
   */
  private extractUserInfoFromPage(): { username?: string; xuid?: string } | null {
    try {
      // 尝试从多个可能的位置获取用户信息
      let userInfo: { username?: string; xuid?: string } | null = null;

      // 辅助函数：获取Cookie值
      const getCookieValue = (name: string): string | null => {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
          const [cookieName, cookieValue] = cookie.trim().split('=');
          if (cookieName === name) {
            return cookieValue;
          }
        }
        return null;
      };

      // 方法1: 检查全局变量
      if ((window as any).userInfo && (window as any).userInfo.uname) {
        userInfo = {
          username: (window as any).userInfo.uname,
          xuid: (window as any).userInfo.xuid || getCookieValue('XUID') || getCookieValue('xuid') || ''
        };
      }

      // 方法2: 检查localStorage
      if (!userInfo) {
        try {
          const storedUserInfo = localStorage.getItem('userInfo');
          if (storedUserInfo) {
            const parsed = JSON.parse(storedUserInfo);
            if (parsed.uname) {
              userInfo = {
                username: parsed.uname,
                xuid: parsed.xuid || getCookieValue('XUID') || getCookieValue('xuid') || ''
              };
            }
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      }

      // 方法3: 检查sessionStorage
      if (!userInfo) {
        try {
          const sessionUserInfo = sessionStorage.getItem('userInfo');
          if (sessionUserInfo) {
            const parsed = JSON.parse(sessionUserInfo);
            if (parsed.uname) {
              userInfo = {
                username: parsed.uname,
                xuid: parsed.xuid || getCookieValue('XUID') || getCookieValue('xuid') || ''
              };
            }
          }
        } catch (e) {
          // 忽略JSON解析错误
        }
      }

      return userInfo;
    } catch (error) {
      console.error('提取用户信息失败:', error);
      return null;
    }
  }

  /**
   * 定期检查并报告用户信息
   */
  private startUserInfoMonitoring(): void {
    // 每5秒检查一次用户信息
    setInterval(() => {
      if (this.isDomainSupported()) {
        const userInfo = this.extractUserInfoFromPage();
        if (userInfo && userInfo.username && userInfo.xuid) {
          // 发送用户信息到后台脚本
          this.sendMessageToBackground({
            action: 'userInfoDetected',
            userInfo: userInfo,
            domain: this.domain
          }).catch((error) => {
            console.debug('发送用户信息失败:', error);
          });
        }
      }
    }, 5000);
  }
}
