import { defineBackground } from 'wxt/utils/define-background';
import * as jsondiffpatch from 'jsondiffpatch';

// 类型定义
interface InterceptRule {
  id: string;
  name: string;
  description?: string;
  enabled: boolean;
  priority: number;
  createdAt: number;
  updatedAt: number;
  conditions: {
    urlPattern: string;
    urlMatchType: 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exact';
    methods?: string[];
  };
  transformation: {
    newUrl: string;
    paramMapping?: Record<string, string>;
    preserveOriginalParams?: boolean;
    includeCookies?: boolean;
  };
  diffConfig?: {
    ignoreFields?: string[];
  };
}

interface DiffReport {
  id: string;
  ruleId: string;
  ruleName: string;
  timestamp: number;
  request: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
    newUrl?: string;
  };
  responses: {
    old: { status: number; body: any; responseTime: number; error?: string };
    new: { status: number; body: any; responseTime: number; error?: string };
  };
  diff: {
    delta: any;
    hasChanges: boolean;
    changeCount: number;
    severity: 'none' | 'minor' | 'major' | 'critical';
  };
  visualizations: {
    html: string;
    summary?: string;
  };
}

// 移除复杂的请求头捕获接口

export default defineBackground(() => {
  console.log('🚀 API迁移验证Service Worker启动');

  // 消息监听器
  browser.runtime.onMessage.addListener((request, sender, sendResponse) => {
    console.log('📨 收到消息:', request);

    if (request.type === 'ping') {
      console.log('🏓 响应ping');
      sendResponse({ success: true, message: 'pong' });
      return true;
    }

    // API迁移消息处理
    if (request.type === 'api-migration') {
      console.log('🎯 处理API迁移消息...');
      handleApiMigrationMessage(request, sender, sendResponse).catch(error => {
        console.error('处理API迁移消息异常:', error);
        sendResponse({ success: false, error: error.message });
      });
      return true;
    }

    // XUID相关消息处理（暂时忽略，避免"未知操作"错误）
    if (request.action === 'xuidDetected' || request.action === 'userInfoDetected') {
      console.log('📋 收到XUID相关消息，暂时忽略:', request.action);
      sendResponse({ success: true, message: 'XUID消息已收到但暂未处理' });
      return true;
    }

    // 对于没有明确type的消息，不处理
    if (!request.type) {
      console.log('⚠️ 收到没有type字段的消息，忽略:', request);
      return false;
    }

    return false;
  });

  // 安装事件
  browser.runtime.onInstalled.addListener(() => {
    console.log('✅ Service Worker已安装');
  });

  // 启动事件
  browser.runtime.onStartup.addListener(() => {
    console.log('🔄 Service Worker已启动');
  });

  console.log('✅ Service Worker初始化完成');

  // 初始化API迁移功能
  initApiMigration().then(() => {
    console.log('✅ API迁移后台功能初始化完成');
  }).catch((error: any) => {
    console.error('❌ API迁移后台功能初始化失败:', error);
  });
});

// API迁移功能变量
let apiMigrationRules: InterceptRule[] = [];
let isIntercepting = false;
let activeRequests = new Map<string, any>(); // 跟踪活跃的请求
let processingRequests = new Set<string>(); // 跟踪正在处理的请求URL，防止死循环

// 域名过滤配置
const domainFilterConfig = {
  enabled: true, // 是否启用域名过滤
  mode: 'whitelist' as 'whitelist' | 'blacklist', // 白名单或黑名单模式
  domains: new Set<string>(), // 缓存的域名列表
  patterns: new Set<string>() // 缓存的URL模式
};

// 预定义的常见API域名（当没有规则时使用）
const commonApiDomains = [
  // 开发测试域名
  'localhost',
  '127.0.0.1',
  'assistantdesk-base-cc.suanshubang.cc',
];

// 初始化JSON差异对比器
const differ = jsondiffpatch.create({
  objectHash: function(obj: any) {
    return obj.id || obj._id || obj.key || JSON.stringify(obj);
  },
  arrays: {
    detectMove: true,
    includeValueOnMove: false
  }
});

// 设置全局jsondiffpatch实例以供其他模块使用
(globalThis as any).jsondiffpatch = jsondiffpatch;

// 初始化API迁移功能
async function initApiMigration() {
  console.log('🔧 API迁移验证工具后台功能已初始化');
  console.log('📋 开始加载API迁移规则...');
  await loadApiMigrationRules();
  console.log('✅ API迁移规则加载完成');

  // 加载拦截器状态
  await loadInterceptingStatus();
  console.log('✅ 拦截器状态加载完成');

  // 更新域名过滤器
  updateDomainFilter();
}

// 更新域名过滤器
function updateDomainFilter() {
  if (!domainFilterConfig.enabled) {
    console.log('🔍 域名过滤已禁用，将拦截所有请求');
    return;
  }

  // 清空现有的域名和模式
  domainFilterConfig.domains.clear();
  domainFilterConfig.patterns.clear();

  // 从规则中提取域名和模式
  for (const rule of apiMigrationRules) {
    if (!rule.enabled) continue;

    const domains = extractDomainsFromRule(rule);
    domains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加域名过滤: ${domain}`);
    });
  }

  // 如果没有从规则中提取到域名，使用预定义的常见API域名
  if (domainFilterConfig.domains.size === 0) {
    console.log('📝 未从规则中提取到域名，使用预定义的常见API域名');
    commonApiDomains.forEach(domain => {
      domainFilterConfig.domains.add(domain);
      console.log(`🎯 添加预定义域名: ${domain}`);
    });
  }

  console.log(`✅ 域名过滤器已更新，共 ${domainFilterConfig.domains.size} 个域名`);
}

// 从规则中提取域名
function extractDomainsFromRule(rule: InterceptRule): string[] {
  const domains: string[] = [];
  const urlPattern = rule.conditions.urlPattern;

  try {
    // 处理不同的URL模式
    switch (rule.conditions.urlMatchType) {
      case 'exact':
        const exactDomain = extractDomainFromUrl(urlPattern);
        if (exactDomain) domains.push(exactDomain);
        break;

      case 'startsWith':
        if (urlPattern.startsWith('http://') || urlPattern.startsWith('https://')) {
          const domain = extractDomainFromUrl(urlPattern);
          if (domain) domains.push(domain);
        }
        break;

      case 'contains':
        // 尝试从包含的字符串中提取域名
        const containsDomain = extractDomainFromPattern(urlPattern);
        if (containsDomain) domains.push(containsDomain);
        break;

      case 'regex':
        // 从正则表达式中提取域名（简单处理）
        const regexDomains = extractDomainsFromRegex(urlPattern);
        domains.push(...regexDomains);
        break;

      case 'endsWith':
        // 对于endsWith，通常是路径匹配，不太适合域名提取
        break;
    }

    // 同时处理transformation.newUrl中的域名
    const newDomain = extractDomainFromUrl(rule.transformation.newUrl);
    if (newDomain && !domains.includes(newDomain)) {
      domains.push(newDomain);
    }
  } catch (error) {
    console.warn(`⚠️ 从规则 ${rule.name} 提取域名失败:`, error);
  }

  return domains;
}

// 从URL中提取域名
function extractDomainFromUrl(url: string): string | null {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    // 如果不是完整URL，尝试其他方法
    const match = url.match(/(?:https?:\/\/)?([^\/\s?#]+)/);
    return match ? match[1] : null;
  }
}

// 从模式中提取域名
function extractDomainFromPattern(pattern: string): string | null {
  // 查找类似域名的模式
  const domainMatch = pattern.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/);
  return domainMatch ? domainMatch[0] : null;
}

// 从正则表达式中提取域名（简单实现）
function extractDomainsFromRegex(regex: string): string[] {
  const domains: string[] = [];

  // 查找正则中的域名模式
  const domainMatches = regex.match(/([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}/g);
  if (domainMatches) {
    domains.push(...domainMatches);
  }

  return domains;
}

// 检查URL是否应该被拦截
function shouldInterceptUrl(url: string): boolean {
  if (!domainFilterConfig.enabled) {
    return true; // 如果禁用过滤，拦截所有请求
  }

  if (domainFilterConfig.domains.size === 0) {
    return true; // 如果没有配置域名，拦截所有请求
  }

  try {
    const urlObj = new URL(url);
    const hostname = urlObj.hostname;

    // 检查精确匹配
    if (domainFilterConfig.domains.has(hostname)) {
      return true;
    }

    // 检查子域名匹配
    for (const domain of domainFilterConfig.domains) {
      if (hostname.endsWith('.' + domain) || hostname === domain) {
        return true;
      }
    }

    return domainFilterConfig.mode === 'blacklist'; // 黑名单模式下，不在列表中的允许通过
  } catch {
    return true; // URL解析失败时，保守地拦截
  }
}

// 加载API迁移规则
async function loadApiMigrationRules() {
  try {
    const result = await browser.storage.local.get(['apiMigrationRules']);
    apiMigrationRules = result.apiMigrationRules || [];
    console.log('📋 已加载', apiMigrationRules.length, '条API迁移规则');
  } catch (error) {
    console.error('加载API迁移规则失败:', error);
    apiMigrationRules = [];
  }
}

// 保存API迁移规则
async function saveApiMigrationRules() {
  try {
    await browser.storage.local.set({ apiMigrationRules });
    console.log('💾 API迁移规则已保存');
  } catch (error) {
    console.error('保存API迁移规则失败:', error);
  }
}

// 加载拦截器状态
async function loadInterceptingStatus() {
  try {
    const result = await browser.storage.local.get(['isIntercepting']);
    isIntercepting = result.isIntercepting || false;
    console.log('📋 已加载拦截器状态:', isIntercepting ? '运行中' : '已停止');

    // 如果状态为运行中，重新注册监听器
    if (isIntercepting) {
      console.log('🔄 重新注册webRequest监听器...');
      if (browser.webRequest && browser.webRequest.onBeforeRequest) {
        browser.webRequest.onBeforeRequest.addListener(
          handleBeforeRequest,
          { urls: ['<all_urls>'] },
          ['requestBody']
        );
        console.log('✅ webRequest.onBeforeRequest 监听器已重新注册');
      }
    }
  } catch (error) {
    console.error('加载拦截器状态失败:', error);
    isIntercepting = false;
  }
}

// 保存拦截器状态
async function saveInterceptingStatus() {
  try {
    await browser.storage.local.set({ isIntercepting });
    console.log('💾 拦截器状态已保存:', isIntercepting ? '运行中' : '已停止');
  } catch (error) {
    console.error('保存拦截器状态失败:', error);
  }
}

// 处理API迁移消息
async function handleApiMigrationMessage(request: any, _sender: any, sendResponse: any) {
  console.log('🔍 处理API迁移消息，完整请求对象:', JSON.stringify(request, null, 2));

  // 兼容两种消息格式：新格式 {type, action, data} 和旧格式 {type, action, ...data}
  const action = request.action;
  const data = request.data || request; // 如果没有data字段，使用整个request对象

  console.log('🎯 解构后的action:', action);
  console.log('🎯 解构后的data:', data);

  // 检查action是否存在
  if (!action) {
    console.log('❌ action字段为空或未定义');
    sendResponse({ success: false, error: 'action字段缺失' });
    return;
  }

  switch (action) {
    case 'getRules':
      console.log('✅ 处理getRules请求，返回', apiMigrationRules.length, '条规则');
      sendResponse({ success: true, data: apiMigrationRules });
      break;

    case 'addRule':
      const newRule: InterceptRule = {
        ...data,
        id: 'rule_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
        createdAt: Date.now(),
        updatedAt: Date.now()
      };
      apiMigrationRules.push(newRule);
      await saveApiMigrationRules();
      updateDomainFilter();
      sendResponse({ success: true, data: newRule });
      break;

    case 'updateRule':
      const ruleIndex = apiMigrationRules.findIndex(r => r.id === data.id);
      if (ruleIndex !== -1) {
        apiMigrationRules[ruleIndex] = { ...data, updatedAt: Date.now() };
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true, data: apiMigrationRules[ruleIndex] });
      } else {
        sendResponse({ success: false, error: '规则不存在' });
      }
      break;

    case 'deleteRule':
      const deleteIndex = apiMigrationRules.findIndex(r => r.id === data.id);
      if (deleteIndex !== -1) {
        apiMigrationRules.splice(deleteIndex, 1);
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: '规则不存在' });
      }
      break;

    case 'startIntercepting':
      await startIntercepting();
      sendResponse({ success: true });
      break;

    case 'stopIntercepting':
      await stopIntercepting();
      sendResponse({ success: true });
      break;

    case 'getReports':
      const reports = await getReports();
      sendResponse({ success: true, data: reports });
      break;

    case 'clearReports':
      await clearReports();
      sendResponse({ success: true });
      break;

    case 'updateRules':
      if (data && data.rules) {
        apiMigrationRules = data.rules;
        await saveApiMigrationRules();
        updateDomainFilter();
        sendResponse({ success: true });
      } else {
        sendResponse({ success: false, error: '无效的规则数据' });
      }
      break;

    case 'getStatus':
      console.log('✅ 处理getStatus请求');
      sendResponse({
        success: true,
        data: {
          isIntercepting: isIntercepting,
          rulesCount: apiMigrationRules.length
        }
      });
      break;

    default:
      console.log('❌ 未知操作:', action, '完整请求:', request);
      sendResponse({ success: false, error: '未知操作' });
  }
}

// 启动拦截
async function startIntercepting() {
  if (isIntercepting) {
    console.log('⚠️ 拦截已经在运行中');
    return;
  }

  try {
    // 注册webRequest监听器
    if (browser.webRequest && browser.webRequest.onBeforeRequest) {
      browser.webRequest.onBeforeRequest.addListener(
        handleBeforeRequest,
        { urls: ['<all_urls>'] },
        ['requestBody']
      );
      console.log('✅ webRequest.onBeforeRequest 监听器已注册');
    }
    
    // 移除复杂的请求头监听器，使用简单直接的方式

    isIntercepting = true;
    await saveInterceptingStatus();
    console.log('🚀 API拦截已启动');
  } catch (error) {
    console.error('❌ 启动API拦截失败:', error);
    throw error;
  }
}

// 停止拦截
async function stopIntercepting() {
  if (!isIntercepting) {
    console.log('⚠️ 拦截未在运行');
    return;
  }

  try {
    // 移除webRequest监听器
    if (browser.webRequest && browser.webRequest.onBeforeRequest) {
      browser.webRequest.onBeforeRequest.removeListener(handleBeforeRequest);
      console.log('✅ webRequest.onBeforeRequest 监听器已移除');
    }
    
    // 移除复杂的请求头监听器清理逻辑

    isIntercepting = false;
    await saveInterceptingStatus();
    console.log('🛑 API拦截已停止');
  } catch (error) {
    console.error('❌ 停止API拦截失败:', error);
    throw error;
  }
}

// 移除复杂的请求头捕获和等待机制

// 增强版请求拦截处理
function handleBeforeRequest(details: any): undefined {
  // 只处理主要的API请求
  if (details.type !== 'main_frame' && details.type !== 'xmlhttprequest' && details.type !== 'fetch') {
    return undefined;
  }

  // 检查是否为内部并行对比请求，避免死循环
  try {
    const url = new URL(details.url);
    if (url.searchParams.has('__api_migration_internal__')) {
      return undefined;
    }
  } catch (error) {
    // URL解析失败，继续处理
  }

  // 域名过滤检查
  if (!shouldInterceptUrl(details.url)) {
    return undefined;
  }

  console.log('🔍 处理API请求:', details.url);

  // 检查是否已经在处理相同的URL，防止死循环
  if (processingRequests.has(details.url)) {
    console.log('⚠️ 请求已在处理中，跳过以防止死循环:', details.url);
    return undefined;
  }

  const rule = findMatchingRule(details.url, details.method);
  if (!rule) {
    return undefined;
  }

  console.log('✅ 找到匹配规则:', rule.name);

  // 标记请求正在处理
  processingRequests.add(details.url);

  // 记录请求信息
  activeRequests.set(details.requestId, {
    url: details.url,
    method: details.method,
    rule: rule,
    timestamp: Date.now(),
    requestBody: details.requestBody,
    requestHeaders: details.requestHeaders  // 保存原始请求头
  });

  try {
    // 并行对比模式 - 真正的并行请求
    console.log('🔄 启动并行对比模式');
    performEnhancedParallelComparison(details, rule).catch((error: any) => {
      console.error('并行对比失败:', error);
    }).finally(() => {
      // 处理完成后清理标记
      processingRequests.delete(details.url);
    });

  } catch (error) {
    console.error('处理API请求失败:', error);
    // 发生错误时也要清理标记
    processingRequests.delete(details.url);
  }

  return undefined;
}

// 查找匹配的规则
function findMatchingRule(url: string, method: string) {
  const enabledRules = apiMigrationRules.filter(rule => rule.enabled)
    .sort((a, b) => b.priority - a.priority);

  for (const rule of enabledRules) {
    if (isRuleMatching(rule, url, method)) {
      return rule;
    }
  }
  return null;
}

// 检查规则是否匹配
function isRuleMatching(rule: InterceptRule, url: string, method: string): boolean {
  // 检查HTTP方法
  if (rule.conditions.methods && rule.conditions.methods.length > 0) {
    if (!rule.conditions.methods.includes(method.toUpperCase())) {
      return false;
    }
  }

  // 检查URL模式
  const pattern = rule.conditions.urlPattern;
  const matchType = rule.conditions.urlMatchType;

  switch (matchType) {
    case 'exact':
      return url === pattern;
    case 'contains':
      return url.includes(pattern);
    case 'startsWith':
      return url.startsWith(pattern);
    case 'endsWith':
      return url.endsWith(pattern);
    case 'regex':
      try {
        const regex = new RegExp(pattern);
        return regex.test(url);
      } catch (error) {
        console.warn('正则表达式无效:', pattern, error);
        return false;
      }
    default:
      return false;
  }
}

// 获取报告
async function getReports(): Promise<DiffReport[]> {
  try {
    // 使用与api-migration-validator.ts相同的storage key格式
    const storageKey = 'tool-api-migration-validator-apiMigrationReports';
    const result = await browser.storage.local.get([storageKey]);
    const reports = result[storageKey] || [];
    
    console.log('📋 获取报告完成:', {
      storageKey,
      报告数量: reports.length,
      包含新接口URL的报告数量: reports.filter((r: DiffReport) => r.request.newUrl).length
    });
    
    return reports;
  } catch (error) {
    console.error('❌ 获取报告失败:', error);
    return [];
  }
}

// 清除报告
async function clearReports() {
  try {
    // 使用与api-migration-validator.ts相同的storage key格式
    const storageKey = 'tool-api-migration-validator-apiMigrationReports';
    await browser.storage.local.remove([storageKey]);
    console.log('✅ 报告已清除');
  } catch (error) {
    console.error('清除报告失败:', error);
  }
}

// 保存报告到存储
async function saveReportToStorage(report: DiffReport) {
  try {
    console.log('💾 开始保存报告:', {
      id: report.id,
      ruleName: report.ruleName,
      originalUrl: report.request.url,
      newUrl: report.request.newUrl,
      timestamp: report.timestamp
    });

    const reports = await getReports();
    reports.push(report);

    // 限制报告数量，保留最新的100个
    if (reports.length > 100) {
      reports.splice(0, reports.length - 100);
    }

    // 使用与api-migration-validator.ts相同的storage key格式
    const storageKey = 'tool-api-migration-validator-apiMigrationReports';
    await browser.storage.local.set({ [storageKey]: reports });
    
    console.log('✅ 报告已保存:', {
      reportId: report.id,
      包含新接口URL: !!report.request.newUrl,
      新接口URL: report.request.newUrl,
      存储中的报告总数: reports.length
    });
  } catch (error) {
    console.error('❌ 保存报告失败:', error);
  }
}

// 简化版并行对比功能 - 直接复制原始请求，只修改URL
async function performEnhancedParallelComparison(details: any, rule: InterceptRule) {
  const startTime = Date.now();
  
  // 直接使用原始请求的头部信息
  const originalHeaders = details.requestHeaders || [];
  
  console.log('🔄 开始并行对比:', { 
    url: details.url, 
    method: details.method,
    ruleName: rule.name,
    includeCookies: rule.transformation.includeCookies,
    originalHeadersCount: Array.isArray(originalHeaders) ? originalHeaders.length : Object.keys(originalHeaders).length
  });

  try {
    // 构建新URL（包含完整参数映射）
    const newUrl = transformUrl(details.url, rule);
    console.log('🎯 目标URL:', newUrl);

    // 准备请求头（直接使用原始请求头）
    const headers = transformHeaders(originalHeaders, rule);
    console.log('📋 请求头处理完成:', {
      原始数量: Array.isArray(originalHeaders) ? originalHeaders.length : Object.keys(originalHeaders).length,
      处理后数量: Object.keys(headers).length,
      包含Cookie: !!headers.cookie,
      包含Authorization: !!headers.authorization
    });

    // 准备请求体（增强版处理）
    let requestBody = details.requestBody;
    if (requestBody && requestBody.raw) {
      // 处理原始请求体
      const decoder = new TextDecoder();
      requestBody = decoder.decode(requestBody.raw[0].bytes);
      console.log('📦 解码请求体完成，长度:', requestBody.length);
    } else if (requestBody && typeof requestBody === 'object') {
      console.log('📦 处理对象类型请求体:', Object.keys(requestBody));
    }

    console.log('🚀 开始并行发送请求...');
    console.log('📊 请求配置对比:', {
      原始请求: {
        url: details.url,
        method: details.method,
        headersCount: Array.isArray(originalHeaders) ? originalHeaders.length : Object.keys(originalHeaders).length,
        hasCookie: Array.isArray(originalHeaders) ? originalHeaders.some((h: any) => h.name.toLowerCase() === 'cookie') : !!originalHeaders.cookie
      },
      新接口请求: {
        url: newUrl,
        method: details.method,
        headersCount: Object.keys(headers).length,
        hasCookie: !!headers.cookie
      }
    });
    
    // 验证参数和Cookie传递
    const paramValidationResult = validateParameterAndCookiePassing(details, newUrl, headers, rule);
    console.log('✅ 参数和Cookie传递验证:', paramValidationResult);
    
        
    // 并行发送两个请求
    const [oldResponse, newResponse] = await Promise.allSettled([
      makeRequest(details.url, details.method, headers, requestBody, rule),
      makeRequest(newUrl, details.method, headers, requestBody, rule)
    ]);

    console.log('📡 并行请求完成，开始处理响应...');

    // 处理响应结果
    const oldResult = oldResponse.status === 'fulfilled' ? oldResponse.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: oldResponse.reason?.message || '原始请求失败'
    };

    const newResult = newResponse.status === 'fulfilled' ? newResponse.value : {
      status: 0,
      body: null,
      responseTime: 0,
      error: newResponse.reason?.message || '新请求失败'
    };

    console.log('📊 响应处理完成:', {
      原始响应: {
        status: oldResult.status,
        responseTime: oldResult.responseTime,
        hasError: 'error' in oldResult && !!oldResult.error
      },
      新接口响应: {
        status: newResult.status,
        responseTime: newResult.responseTime,
        hasError: 'error' in newResult && !!newResult.error
      }
    });

    // 生成差异报告
    const diffResult = generateDiff(oldResult.body, newResult.body, rule);

    // 创建完整报告
    const report: DiffReport = {
      id: 'parallel_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        headers: headers,
        body: requestBody,
        newUrl: newUrl  // 保存实际转换后的URL，而不是规则模板URL
      },
      responses: {
        old: oldResult,
        new: newResult
      },
      diff: diffResult,
      visualizations: {
        html: generateHtmlVisualization(oldResult, newResult, diffResult, rule),
        summary: generateSummary(oldResult, newResult, diffResult)
      }
    };

    // 保存报告
    await saveReportToStorage(report);

    const totalTime = Date.now() - startTime;
    console.log(`✅ 并行对比完成，耗时 ${totalTime}ms:`, report.id);
    console.log('📊 报告已保存，包含新接口URL:', report.request.newUrl);

  } catch (error) {
    console.error('❌ 并行对比失败:', error);

    // 即使在错误情况下，也要尝试生成新URL以便记录
    let errorNewUrl: string | undefined;
    try {
      errorNewUrl = transformUrl(details.url, rule);
      console.log('🔄 错误情况下生成的新URL:', errorNewUrl);
    } catch (urlError) {
      console.warn('⚠️ 错误情况下无法生成新URL:', urlError);
    }

    // 创建错误报告
    const errorReport: DiffReport = {
      id: 'error_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11),
      ruleId: rule.id,
      ruleName: rule.name,
      timestamp: Date.now(),
      request: {
        url: details.url,
        method: details.method,
        headers: details.requestHeaders || {},
        body: details.requestBody,
        newUrl: errorNewUrl  // 在错误报告中也包含新接口URL
      },
      responses: {
        old: { status: 0, body: null, responseTime: 0, error: '对比过程出错' },
        new: { status: 0, body: null, responseTime: 0, error: (error as any)?.message || '未知错误' }
      },
      diff: {
        delta: null,
        hasChanges: true,
        changeCount: 1,
        severity: 'critical'
      },
      visualizations: {
        html: `<div class="error-report"><h4>对比失败</h4><p>错误: ${(error as any)?.message || '未知错误'}</p></div>`,
        summary: `对比失败: ${(error as any)?.message || '未知错误'}`
      }
    };

    await saveReportToStorage(errorReport);
  }
}

// URL转换 - 增强版参数映射
function transformUrl(originalUrl: string, rule: InterceptRule): string {
  console.log('🔄 开始URL转换:', { originalUrl, ruleName: rule.name });

  let newUrl = rule.transformation.newUrl;

  try {
    const url = new URL(originalUrl);
    const newUrlObj = new URL(newUrl);

    // 处理参数映射
    if (rule.transformation.paramMapping) {
      console.log('🗂️ 处理参数映射:', rule.transformation.paramMapping);
      
      // 应用参数映射
      for (const [oldParam, newParam] of Object.entries(rule.transformation.paramMapping)) {
        const value = url.searchParams.get(oldParam);
        if (value !== null) {
          newUrlObj.searchParams.set(newParam, value);
          console.log(`✅ 参数映射: ${oldParam} -> ${newParam} = ${value}`);
          
          if (!rule.transformation.preserveOriginalParams) {
            url.searchParams.delete(oldParam);
          }
        }
      }
    }

    // 保留原始参数（如果配置了）
    if (rule.transformation.preserveOriginalParams) {
      console.log('🔄 保留原始参数');
      let preservedCount = 0;
      
      for (const [key, value] of url.searchParams.entries()) {
        if (!newUrlObj.searchParams.has(key)) {
          newUrlObj.searchParams.set(key, value);
          preservedCount++;
        }
      }
      
      if (preservedCount > 0) {
        console.log(`✅ 保留了 ${preservedCount} 个原始参数`);
      }
    }

    newUrl = newUrlObj.toString();
    console.log('✅ URL转换完成:', { originalUrl, newUrl });

  } catch (error) {
    console.error('❌ URL转换失败，使用原始newUrl:', error);
    // 如果URL解析失败，回退到原始newUrl
  }

  // 添加内部标记，防止死循环
  const finalUrl = new URL(newUrl);
  finalUrl.searchParams.set('__api_migration_internal__', 'true');

  const resultUrl = finalUrl.toString();
  console.log('🎯 最终转换URL:', resultUrl);
  
  return resultUrl;
}

// 请求头转换 - 增强版头部处理，确保完整保留原始请求头
function transformHeaders(originalHeaders: Record<string, string> | any[], rule: InterceptRule): Record<string, string> {
  console.log('🔄 开始处理请求头，原始头部:', Array.isArray(originalHeaders) ? originalHeaders.length : Object.keys(originalHeaders));
  
  // 创建headers的完整副本
  const headers: Record<string, string> = {};
  
  // 复制所有原始头部，确保大小写一致
  if (Array.isArray(originalHeaders)) {
    // 处理 webRequest.HttpHeader[] 格式
    for (const header of originalHeaders) {
      if (header.name && header.value) {
        headers[header.name.toLowerCase()] = header.value;
      }
    }
  } else {
    // 处理 Record<string, string> 格式
    for (const [key, value] of Object.entries(originalHeaders)) {
      headers[key.toLowerCase()] = value;
    }
  }

  console.log('📋 原始头部详情:', {
    totalCount: Array.isArray(originalHeaders) ? originalHeaders.length : Object.keys(originalHeaders).length,
    cookiePresent: !!headers.cookie,
    authorizationPresent: !!headers.authorization,
    originPresent: !!headers.origin,
    refererPresent: !!headers.referer,
    contentTypePresent: !!headers['content-type']
  });

  // 只移除必须由浏览器重新计算的头部
  const headersToRemove = ['content-length', 'host'];
  let removedCount = 0;
  
  for (const header of headersToRemove) {
    if (headers[header]) {
      console.log(`🗑️ 移除头部 ${header}:`, headers[header]);
      delete headers[header];
      removedCount++;
    }
  }

  // 处理Cookie（如果配置了包含Cookie）
  if (rule.transformation.includeCookies) {
    if (headers.cookie) {
      console.log('🍪 保留Cookie头部:', headers.cookie.substring(0, 50) + '...');
    } else {
      console.log('⚠️ 原始请求中没有Cookie头部');
    }
  } else {
    // 如果没有配置包含Cookie，移除Cookie头部
    if (headers.cookie) {
      console.log('🍪 移除Cookie头部（未配置包含Cookie）');
      delete headers.cookie;
    }
  }

  // 保留origin和referer，因为它们对API认证很重要
  console.log('🔗 保留认证相关头部:', {
    origin: headers.origin ? 'Present' : 'Absent',
    referer: headers.referer ? 'Present' : 'Absent'
  });

  console.log('✅ 请求头处理完成:', {
    原始数量: Object.keys(originalHeaders).length,
    最终数量: Object.keys(headers).length,
    移除数量: removedCount,
    保留Cookie: !!headers.cookie
  });
  
  return headers;
}

// 发送HTTP请求 - 增强版Cookie和Header处理
async function makeRequest(url: string, method: string, headers: Record<string, string>, body: any, rule: InterceptRule) {
  const startTime = Date.now();
  console.log('🚀 开始发送请求:', { 
    url, 
    method, 
    includeCookies: rule.transformation.includeCookies,
    headersCount: Object.keys(headers).length 
  });

  try {
    // 创建请求选项
    const requestOptions: RequestInit = {
      method: method,
      // 总是使用include credentials，确保Cookie和其他认证信息能够传递
      credentials: 'include',
      mode: 'cors', // 明确使用cors模式
      redirect: 'follow' // 跟随重定向
    };

    // 构建Headers对象
    const requestHeaders = new Headers();
    
    // 添加所有处理过的headers
    for (const [key, value] of Object.entries(headers)) {
      requestHeaders.set(key, value);
    }

    // 确保关键认证头部被正确设置
    console.log('🔧 设置请求头详情:', {
      cookie: requestHeaders.get('cookie') ? 'Present' : 'Absent',
      authorization: requestHeaders.get('authorization') ? 'Present' : 'Absent',
      origin: requestHeaders.get('origin') ? 'Present' : 'Absent',
      referer: requestHeaders.get('referer') ? 'Present' : 'Absent',
      contentType: requestHeaders.get('content-type') ? 'Present' : 'Absent'
    });

    requestOptions.headers = requestHeaders;

    // 处理请求体
    if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
      console.log('📦 处理请求体，类型:', typeof body);
      
      if (typeof body === 'string') {
        requestOptions.body = body;
      } else if (body && typeof body === 'object') {
        // 如果是对象，尝试JSON序列化
        try {
          requestOptions.body = JSON.stringify(body);
          if (!requestHeaders.has('Content-Type')) {
            requestHeaders.set('Content-Type', 'application/json');
          }
        } catch (stringifyError) {
          console.error('❌ JSON序列化失败:', stringifyError);
          requestOptions.body = String(body);
        }
      } else {
        requestOptions.body = String(body);
      }
    }

    console.log('📤 发送请求选项:', {
      url,
      method,
      credentials: requestOptions.credentials,
      mode: requestOptions.mode,
      headersCount: Object.keys(headers).length,
      hasBody: !!requestOptions.body,
      cookieHeader: requestHeaders.get('cookie') ? 'Present' : 'Absent',
      authorizationHeader: requestHeaders.get('authorization') ? 'Present' : 'Absent'
    });

    const response = await fetch(url, requestOptions);
    const responseTime = Date.now() - startTime;

    console.log('📥 收到响应:', { 
      status: response.status, 
      responseTime,
      contentType: response.headers.get('content-type'),
      responseUrl: response.url
    });

    let responseBody;
    const contentType = response.headers.get('content-type') || '';

    if (contentType.includes('application/json')) {
      try {
        responseBody = await response.json();
      } catch {
        responseBody = await response.text();
      }
    } else {
      responseBody = await response.text();
    }

    console.log('✅ 请求处理完成:', { 
      url, 
      status: response.status, 
      responseTime,
      bodyType: typeof responseBody,
      finalUrl: response.url
    });

    return {
      status: response.status,
      body: responseBody,
      responseTime: responseTime
    };

  } catch (error: any) {
    const responseTime = Date.now() - startTime;
    console.error('❌ 请求失败:', { 
      url, 
      error: error?.message, 
      responseTime,
      errorType: error?.name,
      errorStack: error?.stack 
    });
    
    throw {
      status: 0,
      body: null,
      responseTime: responseTime,
      error: error?.message || '请求失败'
    };
  }
}

// 生成差异对比
function generateDiff(oldData: any, newData: any, rule: InterceptRule) {
  try {
    // 如果配置了忽略字段，先处理数据
    let processedOldData = oldData;
    let processedNewData = newData;

    if (rule.diffConfig?.ignoreFields && Array.isArray(rule.diffConfig.ignoreFields)) {
      processedOldData = removeIgnoredFields(oldData, rule.diffConfig.ignoreFields);
      processedNewData = removeIgnoredFields(newData, rule.diffConfig.ignoreFields);
    }

    // 使用jsondiffpatch生成差异
    const delta = differ.diff(processedOldData, processedNewData);

    // 计算变更统计
    const changeCount = countChanges(delta);
    const hasChanges = changeCount > 0;
    const severity = calculateSeverity(changeCount, oldData, newData);

    return {
      delta: delta,
      hasChanges: hasChanges,
      changeCount: changeCount,
      severity: severity
    };

  } catch (error) {
    console.error('生成差异对比失败:', error);
    return {
      delta: null,
      hasChanges: true,
      changeCount: 1,
      severity: 'critical' as const
    };
  }
}

// 移除忽略的字段
function removeIgnoredFields(data: any, ignoreFields: string[]): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  if (Array.isArray(data)) {
    return data.map(item => removeIgnoredFields(item, ignoreFields));
  }

  const result = { ...data };
  for (const field of ignoreFields) {
    if (field.includes('.')) {
      // 处理嵌套字段，如 'user.id'
      const parts = field.split('.');
      let current = result;
      for (let i = 0; i < parts.length - 1; i++) {
        if (current && typeof current === 'object' && current[parts[i]]) {
          current = current[parts[i]];
        } else {
          break;
        }
      }
      if (current && typeof current === 'object') {
        delete current[parts[parts.length - 1]];
      }
    } else {
      delete result[field];
    }
  }

  return result;
}

// 计算变更数量
function countChanges(delta: any): number {
  if (!delta) return 0;

  let count = 0;

  function traverse(obj: any) {
    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        if (Array.isArray(obj[key]) && obj[key].length === 2) {
          // 这是一个变更 [oldValue, newValue]
          count++;
        } else if (Array.isArray(obj[key]) && obj[key].length === 1) {
          // 这是一个删除 [oldValue]
          count++;
        } else if (Array.isArray(obj[key]) && obj[key].length === 3 && obj[key][2] === 0) {
          // 这是一个添加 [newValue, 0, 0]
          count++;
        } else {
          traverse(obj[key]);
        }
      }
    }
  }

  traverse(delta);
  return count;
}

// 计算严重程度
function calculateSeverity(changeCount: number, oldData: any, newData: any): 'none' | 'minor' | 'major' | 'critical' {
  if (changeCount === 0) return 'none';

  // 检查是否有错误状态
  if ((oldData && oldData.error) || (newData && newData.error)) {
    return 'critical';
  }

  if (changeCount <= 2) return 'minor';
  if (changeCount <= 10) return 'major';
  return 'critical';
}

// 生成HTML可视化
function generateHtmlVisualization(oldResult: any, newResult: any, diffResult: any, rule: InterceptRule): string {
  const severityClass = diffResult.severity;
  const severityMap: Record<string, string> = {
    'none': '无差异',
    'minor': '轻微差异',
    'major': '重要差异',
    'critical': '严重差异'
  };
  const severityText = severityMap[diffResult.severity] || '未知';

  return `
    <div class="api-comparison-report ${severityClass}">
      <div class="report-header">
        <h3>API对比报告</h3>
        <div class="rule-info">
          <span class="rule-name">${rule.name}</span>
          <span class="severity ${severityClass}">${severityText}</span>
        </div>
      </div>

      <div class="response-comparison">
        <div class="response-section">
          <h4>原始API响应</h4>
          <div class="status">状态码: ${oldResult.status}</div>
          <div class="response-time">响应时间: ${oldResult.responseTime}ms</div>
          ${oldResult.error ? `<div class="error">错误: ${oldResult.error}</div>` : ''}
          <pre class="response-body">${JSON.stringify(oldResult.body, null, 2)}</pre>
        </div>

        <div class="response-section">
          <h4>新API响应</h4>
          <div class="status">状态码: ${newResult.status}</div>
          <div class="response-time">响应时间: ${newResult.responseTime}ms</div>
          ${newResult.error ? `<div class="error">错误: ${newResult.error}</div>` : ''}
          <pre class="response-body">${JSON.stringify(newResult.body, null, 2)}</pre>
        </div>
      </div>

      <div class="diff-section">
        <h4>差异详情</h4>
        <div class="diff-stats">
          <span>变更数量: ${diffResult.changeCount}</span>
          <span>严重程度: ${severityText}</span>
        </div>
        ${diffResult.delta ? `<pre class="diff-delta">${JSON.stringify(diffResult.delta, null, 2)}</pre>` : '<p>无差异</p>'}
      </div>
    </div>
  `;
}

// 生成摘要
function generateSummary(oldResult: any, newResult: any, diffResult: any): string {
  if (diffResult.changeCount === 0) {
    return `✅ API响应一致 (${oldResult.responseTime}ms vs ${newResult.responseTime}ms)`;
  }

  if (oldResult.error || newResult.error) {
    return `❌ API请求失败: ${oldResult.error || newResult.error}`;
  }

  const timeDiff = Math.abs(oldResult.responseTime - newResult.responseTime);
  return `⚠️ 发现 ${diffResult.changeCount} 处差异，响应时间差异 ${timeDiff}ms`;
}


// 参数和Cookie传递验证函数 - 简化版
function validateParameterAndCookiePassing(details: any, newUrl: string, headers: Record<string, string>, rule: InterceptRule) {
  console.log('🔍 开始验证参数和Cookie传递...');
  
  try {
    // 验证参数映射
    const originalUrl = new URL(details.url);
    const newUrlObj = new URL(newUrl);
    
    // 提取原始参数
    const originalParams = Array.from(originalUrl.searchParams.keys());
    const newParams = Array.from(newUrlObj.searchParams.keys());
    
    console.log('📊 参数对比:', {
      originalParams,
      newParams,
      paramMapping: rule.transformation.paramMapping,
      preserveOriginalParams: rule.transformation.preserveOriginalParams
    });

    // 验证Cookie处理
    if (rule.transformation.includeCookies) {
      const cookieHeader = headers.cookie;
      if (cookieHeader) {
        console.log('✅ Cookie传递验证成功:', {
          cookiePresent: true,
          cookieLength: cookieHeader.length,
          cookiePreview: cookieHeader.substring(0, 50) + '...'
        });
      } else {
        console.log('⚠️ Cookie传递验证失败: 原始请求中没有Cookie');
      }
    }

    console.log('✅ 参数和Cookie传递验证完成');

  } catch (error) {
    console.error('❌ 参数和Cookie传递验证失败:', error);
  }
}
