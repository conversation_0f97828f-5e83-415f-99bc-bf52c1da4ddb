/**
 * 工具模板和接口定义
 * 用于创建新工具时的标准化模板
 */

// 版本比较工具函数
export class VersionUtils {
  static parseVersion(versionString: string): ToolVersion {
    const parts = versionString.split('.');
    const [major, minor, patch] = parts.map(p => parseInt(p) || 0);

    let prerelease: string | undefined;
    if (parts[2] && parts[2].includes('-')) {
      const [patchPart, prereleasePart] = parts[2].split('-');
      prerelease = prereleasePart;
    }

    return { major, minor, patch, prerelease };
  }

  static versionToString(version: ToolVersion): string {
    let versionStr = `${version.major}.${version.minor}.${version.patch}`;
    if (version.prerelease) {
      versionStr += `-${version.prerelease}`;
    }
    return versionStr;
  }

  static compareVersions(v1: ToolVersion, v2: ToolVersion): number {
    if (v1.major !== v2.major) return v1.major - v2.major;
    if (v1.minor !== v2.minor) return v1.minor - v2.minor;
    if (v1.patch !== v2.patch) return v1.patch - v2.patch;

    // 处理预发布版本
    if (v1.prerelease && !v2.prerelease) return -1;
    if (!v1.prerelease && v2.prerelease) return 1;
    if (v1.prerelease && v2.prerelease) {
      return v1.prerelease.localeCompare(v2.prerelease);
    }

    return 0;
  }

  static isVersionCompatible(required: string, actual: ToolVersion): boolean {
    if (required.startsWith('^')) {
      // 兼容主版本
      const requiredVersion = this.parseVersion(required.slice(1));
      return actual.major === requiredVersion.major &&
             this.compareVersions(actual, requiredVersion) >= 0;
    } else if (required.startsWith('~')) {
      // 兼容次版本
      const requiredVersion = this.parseVersion(required.slice(1));
      return actual.major === requiredVersion.major &&
             actual.minor === requiredVersion.minor &&
             this.compareVersions(actual, requiredVersion) >= 0;
    } else {
      // 精确匹配
      const requiredVersion = this.parseVersion(required);
      return this.compareVersions(actual, requiredVersion) === 0;
    }
  }
}

// 工具依赖定义
export interface ToolDependency {
  id: string;
  version?: string;
  optional?: boolean;
}

// 工具版本信息
export interface ToolVersion {
  major: number;
  minor: number;
  patch: number;
  prerelease?: string;
}

// 工具生命周期状态
export type ToolLifecycleState = 'uninitialized' | 'initializing' | 'ready' | 'running' | 'error' | 'destroyed';

// 工具配置接口
export interface ToolConfig {
  [key: string]: any;
}

// 工具接口（从main.ts导入）
export interface Tool {
  id: string;
  name: string;
  description: string;
  icon: string;
  categories: string[]; // 改为支持多分类标签
  enabled: boolean;
  badge?: 'new' | 'beta';
  position: number; // 添加排序位置字段

  // 版本控制
  version: ToolVersion;

  // 依赖管理
  dependencies?: ToolDependency[];

  // 生命周期状态
  lifecycleState?: ToolLifecycleState;

  // 配置
  config?: ToolConfig;

  // 权限要求
  permissions?: string[];

  // 主要动作
  action: () => void | Promise<void>;

  // 生命周期钩子
  onInit?: () => void | Promise<void>;
  onDestroy?: () => void | Promise<void>;
  onEnable?: () => void | Promise<void>;
  onDisable?: () => void | Promise<void>;
}

// 工具基类
export abstract class BaseTool implements Tool {
  abstract id: string;
  abstract name: string;
  abstract description: string;
  abstract icon: string;
  abstract categories: string[]; // 改为支持多分类标签

  enabled: boolean = true;
  badge?: 'new' | 'beta';
  position: number = 0; // 默认排序位置

  // 版本控制 - 子类可以覆盖
  version: ToolVersion = { major: 1, minor: 0, patch: 0 };

  // 依赖管理
  dependencies?: ToolDependency[];

  // 生命周期状态
  lifecycleState: ToolLifecycleState = 'uninitialized';

  // 配置
  config?: ToolConfig;

  // 权限要求
  permissions?: string[];

  abstract action(): void | Promise<void>;

  // 生命周期钩子 - 子类可以覆盖
  async onInit(): Promise<void> {
    this.lifecycleState = 'initializing';
    // 默认初始化逻辑
    this.lifecycleState = 'ready';
  }

  async onDestroy(): Promise<void> {
    this.lifecycleState = 'destroyed';
    // 默认销毁逻辑
  }

  async onEnable(): Promise<void> {
    this.enabled = true;
    // 默认启用逻辑
  }

  async onDisable(): Promise<void> {
    this.enabled = false;
    // 默认禁用逻辑
  }

  // 工具间通信机制
  protected async sendMessage(targetToolId: string, message: any): Promise<any> {
    // 通过事件系统发送消息
    const event = new CustomEvent('tool-message', {
      detail: {
        from: this.id,
        to: targetToolId,
        message: message,
        timestamp: Date.now()
      }
    });

    document.dispatchEvent(event);

    // 返回Promise，等待响应
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`工具 ${targetToolId} 响应超时`));
      }, 5000);

      const responseHandler = (event: CustomEvent) => {
        const { from, to, response } = event.detail;
        if (from === targetToolId && to === this.id) {
          clearTimeout(timeout);
          document.removeEventListener('tool-response', responseHandler as EventListener);
          resolve(response);
        }
      };

      document.addEventListener('tool-response', responseHandler as EventListener);
    });
  }

  // 监听来自其他工具的消息
  protected onMessage(handler: (from: string, message: any) => any): void {
    const messageHandler = (event: CustomEvent) => {
      const { from, to, message } = event.detail;
      if (to === this.id) {
        try {
          const response = handler(from, message);

          // 发送响应
          const responseEvent = new CustomEvent('tool-response', {
            detail: {
              from: this.id,
              to: from,
              response: response
            }
          });

          document.dispatchEvent(responseEvent);
        } catch (error) {
          console.error(`工具 ${this.id} 处理消息失败:`, error);
        }
      }
    };

    document.addEventListener('tool-message', messageHandler as EventListener);
  }

  // 配置管理
  protected async getConfig<T = any>(key: string, defaultValue?: T): Promise<T> {
    if (!this.config) {
      this.config = {};
    }

    return this.config[key] !== undefined ? this.config[key] : defaultValue;
  }

  protected async setConfig(key: string, value: any): Promise<void> {
    if (!this.config) {
      this.config = {};
    }

    this.config[key] = value;

    // 通知配置变更
    const event = new CustomEvent('tool-config-changed', {
      detail: {
        toolId: this.id,
        key: key,
        value: value
      }
    });

    document.dispatchEvent(event);
  }

  // 数据持久化
  protected async saveData(key: string, data: any): Promise<void> {
    const storageKey = `tool-${this.id}-${key}`;
    try {
      await browser.storage.local.set({ [storageKey]: data });
    } catch (error) {
      console.error(`保存数据失败 (${key}):`, error);
      throw error;
    }
  }

  protected async loadData<T = any>(key: string, defaultValue?: T): Promise<T> {
    const storageKey = `tool-${this.id}-${key}`;
    try {
      const result = await browser.storage.local.get(storageKey);
      return result[storageKey] !== undefined ? result[storageKey] : defaultValue;
    } catch (error) {
      console.error(`加载数据失败 (${key}):`, error);
      return defaultValue as T;
    }
  }

  protected async removeData(key: string): Promise<void> {
    const storageKey = `tool-${this.id}-${key}`;
    try {
      await browser.storage.local.remove(storageKey);
    } catch (error) {
      console.error(`删除数据失败 (${key}):`, error);
      throw error;
    }
  }

  // HTTP请求封装
  protected async httpRequest(url: string, options: RequestInit = {}): Promise<Response> {
    const defaultOptions: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': `fwyy-tools/${this.id}`
      }
    };

    const mergedOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    };

    try {
      const response = await fetch(url, mergedOptions);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return response;
    } catch (error) {
      console.error(`HTTP请求失败 (${url}):`, error);
      throw error;
    }
  }

  // JSON HTTP请求
  protected async httpJSON<T = any>(url: string, options: RequestInit = {}): Promise<T> {
    const response = await this.httpRequest(url, options);
    return await response.json();
  }

  // 表单验证
  protected validateForm(form: HTMLFormElement, rules: Record<string, (value: string) => string | null>): Record<string, string> {
    const errors: Record<string, string> = {};
    const formData = new FormData(form);

    for (const [fieldName, validator] of Object.entries(rules)) {
      const value = formData.get(fieldName) as string || '';
      const error = validator(value);
      if (error) {
        errors[fieldName] = error;
      }
    }

    return errors;
  }

  // 常用验证器
  protected static validators = {
    required: (value: string) => value.trim() ? null : '此字段为必填项',
    email: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value) ? null : '请输入有效的邮箱地址',
    url: (value: string) => {
      try {
        new URL(value);
        return null;
      } catch {
        return '请输入有效的URL';
      }
    },
    minLength: (min: number) => (value: string) => value.length >= min ? null : `最少需要${min}个字符`,
    maxLength: (max: number) => (value: string) => value.length <= max ? null : `最多允许${max}个字符`,
    pattern: (regex: RegExp, message: string) => (value: string) => regex.test(value) ? null : message
  };

  // 防抖函数
  protected debounce<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let timeoutId: NodeJS.Timeout;
    return ((...args: any[]) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => func.apply(this, args), delay);
    }) as T;
  }

  // 节流函数
  protected throttle<T extends (...args: any[]) => any>(func: T, delay: number): T {
    let lastCall = 0;
    return ((...args: any[]) => {
      const now = Date.now();
      if (now - lastCall >= delay) {
        lastCall = now;
        return func.apply(this, args);
      }
    }) as T;
  }

  // 通用方法
  protected async getCurrentTab(): Promise<Browser.tabs.Tab | undefined> {
    const [tab] = await browser.tabs.query({ active: true, currentWindow: true });
    return tab;
  }

  protected async executeScript(func: () => void): Promise<any> {
    const tab = await this.getCurrentTab();
    if (!tab?.id) throw new Error('无法获取当前标签页');
    
    return browser.scripting.executeScript({
      target: { tabId: tab.id },
      func: func
    });
  }

  protected async showNotification(title: string, message: string): Promise<void> {
    browser.notifications.create({
      type: 'basic',
      iconUrl: '/icon/48.png',
      title: title,
      message: message
    });
  }

  protected async copyToClipboard(text: string): Promise<void> {
    await navigator.clipboard.writeText(text);
  }

  protected async downloadFile(content: string, filename: string, type: string = 'text/plain'): Promise<void> {
    const blob = new Blob([content], { type });
    const url = URL.createObjectURL(blob);
    
    await browser.downloads.download({
      url: url,
      filename: filename,
      saveAs: true
    });
    
    URL.revokeObjectURL(url);
  }
}

// 工具创建器辅助函数
export function createTool(config: {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'productivity' | 'development' | 'utility';
  enabled?: boolean;
  badge?: 'new' | 'beta';
  action: () => void | Promise<void>;
}): Tool {
  return {
    enabled: true,
    ...config
  };
}

// 示例工具模板
export class ExampleTool extends BaseTool {
  id = 'example-tool';
  name = '示例工具';
  description = '这是一个示例工具模板';
  icon = '🛠️';
  categories: string[] = ['all']; // 使用新的多分类标签格式
  badge: 'new' = 'new';

  async action(): Promise<void> {
    try {
      // 获取当前标签页
      const tab = await this.getCurrentTab();
      console.log('当前标签页:', tab);

      // 执行内容脚本
      await this.executeScript(() => {
        console.log('在页面中执行的脚本');
        return document.title;
      });

      // 显示通知
      await this.showNotification('工具执行成功', '示例工具已完成操作');

      // 复制到剪贴板
      await this.copyToClipboard('示例文本');

    } catch (error) {
      console.error('工具执行失败:', error);
      await this.showNotification('工具执行失败', error.message);
    }
  }
}

// 工具分类常量
export const TOOL_CATEGORIES = {
  PRODUCTIVITY: 'productivity' as const,
  DEVELOPMENT: 'development' as const,
  UTILITY: 'utility' as const
} as const;

// 工具徽章常量
export const TOOL_BADGES = {
  NEW: 'new' as const,
  BETA: 'beta' as const
} as const;

// 常用图标
export const TOOL_ICONS = {
  // 开发工具
  CODE: '💻',
  DEBUG: '🐛',
  API: '🔌',
  DATABASE: '🗄️',
  GIT: '📝',
  
  // 效率工具
  CLOCK: '⏰',
  CALENDAR: '📅',
  NOTE: '📝',
  TASK: '✅',
  SEARCH: '🔍',
  
  // 实用工具
  TOOL: '🛠️',
  SETTINGS: '⚙️',
  DOWNLOAD: '⬇️',
  UPLOAD: '⬆️',
  COPY: '📋',
  
  // 媒体工具
  IMAGE: '🖼️',
  VIDEO: '🎥',
  AUDIO: '🎵',
  CAMERA: '📸',
  
  // 网络工具
  LINK: '🔗',
  WIFI: '📶',
  GLOBE: '🌐',
  EMAIL: '📧',
  
  // 安全工具
  LOCK: '🔐',
  KEY: '🔑',
  SHIELD: '🛡️',
  
  // 文件工具
  FILE: '📄',
  FOLDER: '📁',
  ZIP: '🗜️',
  PDF: '📕'
} as const;
