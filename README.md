# 服务运营工具集合

一个基于WXT框架开发的现代化浏览器扩展，提供各种实用的开发和日常工具。

## 🚀 特性

- 🛠️ **模块化设计**: 每个工具都是独立的模块，易于维护和扩展
- 🎨 **现代化界面**: 响应式设计，支持明暗主题切换
- 🔍 **智能搜索**: 快速查找所需工具
- 📱 **多分类管理**: 支持动态分类标签，工具可属于多个分类
- 🔄 **版本管理**: 自动检查工具更新，支持版本控制
- 🎯 **拖拽排序**: 支持工具卡片拖拽重新排序
- 🔧 **工具生成器**: 一键生成新工具模板
- 📋 **模态框系统**: 统一的模态框组件，提供更好的用户体验
- ⚡ **高性能**: 基于WXT框架，支持热重载和快速构建
- 🌐 **多浏览器支持**: 兼容Chrome、Firefox、Edge等主流浏览器
- 💾 **本地存储**: 所有数据本地存储，保护用户隐私
- 🔔 **通知系统**: 统一的通知管理，支持多种通知类型

## 📦 已包含的工具

### 🚨 监控工具
- **告警解析器** (`alert-parser`): 解析监控告警信息，自动生成Grafana日志查询链接，支持多种告警格式解析

### � 实用工具
- **XUID工具** (`xuid`): 基础实用工具，提供常用功能

## 🛠️ 开发环境

### 前置要求
- Node.js 16+
- npm 或 pnpm

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev          # Chrome开发模式
npm run dev:firefox  # Firefox开发模式
```

### 构建生产版本
```bash
npm run build        # 构建Chrome版本
npm run build:firefox # 构建Firefox版本
```

### 打包发布
```bash
npm run zip          # 打包Chrome版本
npm run zip:firefox  # 打包Firefox版本
```

## 📁 项目结构

```
fwyy-tools/
├── entrypoints/          # 扩展入口点
│   ├── background.ts     # 后台脚本 - 处理扩展生命周期
│   ├── content.ts        # 内容脚本 - 页面交互
│   └── popup/           # 弹出窗口 - 主界面
│       ├── index.html   # 主界面HTML结构
│       ├── main.ts      # 主界面逻辑控制
│       └── style.css    # 主界面样式
├── utils/               # 工具类和模板系统
│   ├── tool-template.ts # 工具基类和接口定义
│   ├── tool-registry.ts # 工具注册管理系统
│   ├── ui-components.ts # UI组件库
│   ├── settings-manager.ts # 设置管理器
│   ├── style-manager.ts # 样式管理器
│   ├── version-manager.ts # 版本管理器
│   ├── notification-manager.ts # 通知管理器
│   └── category-manager.ts # 分类管理器
├── tools/               # 具体工具实现
├── styles/             # 统一设计系统
│   ├── design-tokens.css # 设计令牌定义
│   ├── components.css   # 组件样式
│   ├── layout.css      # 布局样式
│   ├── utilities.css   # 工具类样式
│   ├── animations.css  # 动画效果
│   └── notifications.css # 通知样式
├── scripts/            # 开发脚本
│   └── create-tool.js  # 工具生成器脚本
├── docs/               # 项目文档
│   ├── EXTENSIBILITY_GUIDE.md # 可扩展性架构指南
│   ├── MIGRATION_GUIDE.md # 工具迁移指南
│   └── STYLE_GUIDE.md  # 样式管理指南
├── components/          # 可复用组件
├── assets/             # 静态资源
├── public/             # 公共文件和图标
├── rule-file.md        # 开发规范文档
├── wxt.config.ts       # WXT配置
├── tsconfig.json       # TypeScript配置
└── package.json        # 项目配置
```

## 🔧 添加新工具

### 方法一：使用工具生成器（推荐）

```bash
# 使用npm脚本生成新工具
npm run create-tool

# 或者使用别名
npm run tool:create
```

工具生成器将引导您完成以下步骤：
1. 输入工具ID（kebab-case格式）
2. 设置工具名称和描述
3. 选择工具图标（emoji）
4. 选择工具分类（productivity/development/utility）
5. 选择工具类型（basic/ui/api/content）

### 方法二：手动创建工具

#### 1. 使用工具模板
```typescript
import { BaseTool, TOOL_CATEGORIES, TOOL_ICONS } from '@/utils/tool-template';

export class MyTool extends BaseTool {
  id = 'my-tool';
  name = '我的工具';
  description = '工具描述';
  icon = '🛠️';
  categories = ['all']; // 使用新的多分类标签格式
  version = { major: 1, minor: 0, patch: 0 };
  badge?: 'new' = 'new'; // 可选：添加徽章

  async action(): Promise<void> {
    try {
      // 实现工具功能
      console.log('执行我的工具');

      // 显示通知
      await this.showNotification('成功', '工具执行完成');

      // 复制到剪贴板
      await this.copyToClipboard('结果文本');

    } catch (error) {
      console.error('工具执行失败:', error);
      await this.showNotification('错误', `工具执行失败: ${error.message}`);
    }
  }
}
```

#### 2. 注册工具
```typescript
import { toolRegistry } from '@/utils/tool-registry';
import { MyTool } from './my-tool';

// 注册工具实例
toolRegistry.register(new MyTool());
```

#### 3. 工具权限配置
在 `wxt.config.ts` 中添加所需权限：
```typescript
permissions: [
  'activeTab',     // 访问当前标签页
  'storage',       // 本地存储
  'tabs',          // 标签页管理
  'scripting',     // 脚本注入
  'notifications', // 通知
  'downloads',     // 下载文件
  'clipboardWrite',// 剪贴板写入
  'cookies'        // Cookie访问
]
```

## �️ 核心架构

### 工具系统
- **BaseTool**: 所有工具的基类，提供统一的接口和功能
- **ToolRegistry**: 工具注册和管理系统，支持依赖管理
- **VersionManager**: 版本管理系统，支持自动更新检查
- **CategoryManager**: 分类管理系统，支持动态分类标签

### UI系统
- **UIComponents**: 统一的UI组件库，包含模态框、按钮等
- **StyleManager**: 样式管理系统，支持主题切换
- **NotificationManager**: 通知管理系统，支持多种通知类型

### 数据管理
- **SettingsManager**: 设置管理系统，处理用户配置
- **本地存储**: 使用Chrome扩展API进行数据持久化

## �🎨 界面定制

### 设计令牌系统
项目使用设计令牌系统，在 `styles/design-tokens.css` 中定义：
```css
:root {
  /* 颜色系统 */
  --primary-color: #4f46e5;
  --primary-hover: #4338ca;
  --background: #ffffff;
  --surface: #f9fafb;
  --text-primary: #111827;
  --text-secondary: #6b7280;

  /* 间距系统 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;

  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
}
```

### 组件样式
- `components.css`: 通用组件样式
- `layout.css`: 布局相关样式
- `utilities.css`: 工具类样式
- `animations.css`: 动画效果
- `notifications.css`: 通知样式

### 响应式设计
界面支持不同屏幕尺寸，在小屏幕上会自动调整布局。

## 📋 工具分类系统

### 默认分类
- **all**: 全部工具（默认分类）
- **productivity**: 效率工具
- **development**: 开发工具
- **utility**: 实用工具

### 动态分类管理
- 支持动态添加和删除分类标签
- 工具可以属于多个分类
- 分类标签支持拖拽排序
- 自动清理无工具的空分类

## 🔍 搜索功能

- **智能搜索**: 支持按工具名称、描述和ID进行模糊搜索
- **实时过滤**: 输入时实时显示搜索结果
- **高亮显示**: 搜索结果中关键词高亮显示
- **快捷键**: 支持键盘快捷键快速搜索

## 🔄 版本管理

- **自动检查**: 定期检查工具更新
- **版本控制**: 支持语义化版本管理（major.minor.patch）
- **更新通知**: 有新版本时显示更新徽章
- **批量更新**: 支持批量检查和更新工具

## 💾 数据存储

- **本地存储**: 使用Chrome扩展的本地存储API，数据仅保存在本地
- **设置同步**: 支持用户设置和工具配置的本地持久化
- **数据安全**: 敏感数据支持加密存储
- **导入导出**: 支持配置数据的导入和导出

## 🚀 部署发布

1. 构建生产版本：`npm run build`
2. 打包为zip：`npm run zip`
3. 上传到Chrome Web Store或Firefox Add-ons

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 📚 开发文档

### 核心文档
- [开发规范文档](./rule-file.md) - 项目架构和开发规范
- [可扩展性架构指南](./docs/EXTENSIBILITY_GUIDE.md) - 工具生成器、依赖管理、版本控制等
- [工具迁移指南](./docs/MIGRATION_GUIDE.md) - 如何将现有扩展功能迁移到本项目
- [样式管理指南](./docs/STYLE_GUIDE.md) - 样式系统和设计规范

### 核心模块
- [工具基类模板](./utils/tool-template.ts) - 创建新工具的标准模板
- [工具注册系统](./utils/tool-registry.ts) - 工具管理和注册机制
- [版本管理系统](./utils/version-manager.ts) - 工具版本控制和更新管理
- [UI组件库](./utils/ui-components.ts) - 统一的UI组件系统
- [设置管理器](./utils/settings-manager.ts) - 用户设置和配置管理

### 开发工具
- [工具生成器](./scripts/create-tool.js) - 自动生成新工具的脚本

## 🔗 相关链接

### 框架文档
- [WXT框架文档](https://wxt.dev/) - 现代化Web扩展开发框架
- [Chrome扩展开发文档](https://developer.chrome.com/docs/extensions/) - Chrome扩展官方文档
- [Firefox扩展开发文档](https://developer.mozilla.org/en-US/docs/Mozilla/Add-ons/WebExtensions) - Firefox扩展官方文档

### 技术栈
- [TypeScript文档](https://www.typescriptlang.org/docs/) - TypeScript官方文档
- [Vite文档](https://vitejs.dev/) - 现代化构建工具
- [CSS设计令牌](https://design-tokens.github.io/community-group/) - 设计令牌规范
