{"name": "fwyy-tools", "description": "一个功能丰富的浏览器扩展工具集合", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "npm run setup-styles && wxt", "dev:firefox": "npm run setup-styles && wxt -b firefox", "build": "npm run setup-styles && wxt build", "build:firefox": "npm run setup-styles && wxt build -b firefox", "zip": "wxt zip", "zip:firefox": "wxt zip -b firefox", "compile": "tsc --noEmit", "postinstall": "wxt prepare && npm run setup-styles", "create-tool": "node scripts/create-tool.js", "tool:create": "node scripts/create-tool.js", "setup-styles": "mkdir -p public/entrypoints/popup && [ ! -L public/styles ] && ln -sf ../styles public/styles || true && [ ! -L public/entrypoints/popup/style.css ] && ln -sf ../../../entrypoints/popup/style.css public/entrypoints/popup/style.css || true"}, "devDependencies": {"typescript": "^5.8.3", "wxt": "^0.20.6"}, "dependencies": {"@mswjs/interceptors": "^0.39.5", "jsondiffpatch": "^0.7.3"}}