/**
 * UI组件库样式
 * 基于设计令牌系统的可复用组件
 */

/* ========== 基础重置 ========== */
*,
*::before,
*::after {
  box-sizing: border-box;
}

/* ========== 按钮组件 ========== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--button-padding-y) var(--button-padding-x);
  border: 1px solid transparent;
  border-radius: var(--button-radius);
  font-family: inherit;
  font-size: var(--font-size-sm);
  font-weight: var(--button-font-weight);
  line-height: var(--line-height-tight);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-in-out);
  user-select: none;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮变体 */
.btn-primary {
  background-color: var(--primary);
  color: white;
  border-color: var(--primary);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-primary:active:not(:disabled) {
  background-color: var(--primary-active);
  border-color: var(--primary-active);
}

.btn-secondary {
  background-color: var(--surface);
  color: var(--text-primary);
  border-color: var(--border);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--surface-hover);
  border-color: var(--border-hover);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary);
  border-color: var(--primary);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-light);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-primary);
  border-color: transparent;
}

.btn-ghost:hover:not(:disabled) {
  background-color: var(--surface-hover);
}

/* 按钮尺寸 */
.btn-sm {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
}

/* 状态按钮 */
.btn-success {
  background-color: var(--success);
  color: white;
  border-color: var(--success);
}

.btn-warning {
  background-color: var(--warning);
  color: white;
  border-color: var(--warning);
}

.btn-error {
  background-color: var(--error);
  color: white;
  border-color: var(--error);
}

/* ========== 表单组件 ========== */
.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--input-padding-y) var(--input-padding-x);
  border: var(--input-border-width) solid var(--border);
  border-radius: var(--input-radius);
  background-color: var(--background);
  color: var(--text-primary);
  font-family: inherit;
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  transition: border-color var(--duration-150) var(--ease-in-out),
              box-shadow var(--duration-150) var(--ease-in-out);
}

.form-control:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-control:disabled {
  background-color: var(--surface);
  color: var(--text-disabled);
  cursor: not-allowed;
}

.form-control::placeholder {
  color: var(--text-tertiary);
}

/* 表单控件尺寸 */
.form-control-sm {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
}

.form-control-lg {
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
}

/* 文本域 */
.form-textarea {
  resize: vertical;
  min-height: 80px;
}

/* 选择框 */
.form-select {
  appearance: none; /* 隐藏浏览器默认的下拉箭头 */
  -webkit-appearance: none; /* Safari 兼容性 */
  -moz-appearance: none; /* Firefox 兼容性 */
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right var(--spacing-2) center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: var(--spacing-8);
}

/* 复选框和单选框 */
.form-check {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-2);
}

.form-check-input {
  width: 1rem;
  height: 1rem;
  margin: 0;
  accent-color: var(--primary);
}

.form-check-label {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  cursor: pointer;
}

/* 表单验证状态 */
.form-control.is-valid {
  border-color: var(--success);
}

.form-control.is-invalid {
  border-color: var(--error);
}

.form-feedback {
  margin-top: var(--spacing-1);
  font-size: var(--font-size-xs);
}

.form-feedback.valid-feedback {
  color: var(--success);
}

.form-feedback.invalid-feedback {
  color: var(--error);
}

/* ========== 卡片组件 ========== */
.card {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--card-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
}

.card-header {
  padding: var(--card-padding);
  border-bottom: 1px solid var(--border);
  background-color: var(--background);
}

.card-body {
  padding: var(--card-padding);
}

.card-footer {
  padding: var(--card-padding);
  border-top: 1px solid var(--border);
  background-color: var(--background);
}

.card-title {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.card-text {
  margin: 0;
  color: var(--text-secondary);
}

/* ========== 模态框组件 ========== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: var(--z-index-modal);
  padding: var(--spacing-4);
}

.modal-content {
  background-color: var(--background);
  border-radius: var(--modal-radius);
  box-shadow: var(--modal-shadow);
  max-width: 420px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 模态框尺寸变体 */
.modal-sm {
  max-width: 320px;
}

.modal-md {
  max-width: 420px;
}

.modal-lg {
  max-width: 640px;
}

.modal-xl {
  max-width: 800px;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--modal-header-padding);
  border-bottom: 1px solid var(--border);
  min-height: var(--modal-header-min-height);
}

.modal-title {
  margin: 0;
  font-size: var(--modal-title-font-size);
  font-weight: var(--modal-title-font-weight);
  color: var(--text-primary);
}

.modal-close {
  background: none;
  border: none;
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--radius-base);
  transition: color var(--duration-150) var(--ease-in-out);
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: var(--modal-body-padding);
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-3);
  padding: var(--modal-footer-padding);
  border-top: 1px solid var(--border);
  min-height: var(--modal-footer-min-height);
}

/* ========== 工具提示组件 ========== */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-content {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  background-color: var(--text-primary);
  color: var(--background);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-base);
  white-space: nowrap;
  z-index: var(--z-index-tooltip);
  opacity: 0;
  visibility: hidden;
  transition: opacity var(--duration-150) var(--ease-in-out),
              visibility var(--duration-150) var(--ease-in-out);
}

.tooltip:hover .tooltip-content {
  opacity: 1;
  visibility: visible;
}

/* ========== 徽章组件 ========== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.badge-primary {
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

.badge-success {
  background-color: var(--color-success-100);
  color: var(--color-success-700);
}

.badge-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-700);
}

.badge-error {
  background-color: var(--color-error-100);
  color: var(--color-error-700);
}

.badge-gray {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

/* ========== 加载器组件 ========== */
.spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--border);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin var(--duration-1000) linear infinite;
}

.spinner-sm {
  width: 0.75rem;
  height: 0.75rem;
  border-width: 1px;
}

.spinner-lg {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 3px;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* ========== 进度条组件 ========== */
.progress {
  width: 100%;
  height: var(--spacing-2);
  background-color: var(--surface);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: var(--primary);
  border-radius: var(--radius-full);
  transition: width var(--duration-300) var(--ease-out);
}

.progress-bar-success {
  background-color: var(--success);
}

.progress-bar-warning {
  background-color: var(--warning);
}

.progress-bar-error {
  background-color: var(--error);
}

/* ========== 分隔线组件 ========== */
.divider {
  border: none;
  height: 1px;
  background-color: var(--border);
  margin: var(--spacing-4) 0;
}

.divider-vertical {
  width: 1px;
  height: auto;
  margin: 0 var(--spacing-4);
}

/* ========== 列表组件 ========== */
.list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border);
  transition: background-color var(--duration-150) var(--ease-in-out);
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:hover {
  background-color: var(--surface-hover);
}

.list-item-clickable {
  cursor: pointer;
}

.list-item-icon {
  margin-right: var(--spacing-3);
  color: var(--text-secondary);
}

.list-item-content {
  flex: 1;
}

.list-item-title {
  margin: 0 0 var(--spacing-1) 0;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.list-item-description {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.list-item-action {
  margin-left: var(--spacing-3);
}

/* ========== 标签页组件 ========== */
.tabs {
  border-bottom: 1px solid var(--border);
}

.tabs-list {
  display: flex;
  list-style: none;
  padding: 0;
  margin: 0;
}

.tabs-item {
  margin-right: var(--spacing-1);
}

.tabs-link {
  display: block;
  padding: var(--spacing-3) var(--spacing-4);
  color: var(--text-secondary);
  text-decoration: none;
  border-bottom: 2px solid transparent;
  transition: color var(--duration-150) var(--ease-in-out),
              border-color var(--duration-150) var(--ease-in-out);
}

.tabs-link:hover {
  color: var(--text-primary);
}

.tabs-link.active {
  color: var(--primary);
  border-bottom-color: var(--primary);
}

.tabs-content {
  padding: var(--spacing-4) 0;
}

/* ========== 下拉菜单组件 ========== */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  min-width: 200px;
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-index-dropdown);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: opacity var(--duration-150) var(--ease-out),
              visibility var(--duration-150) var(--ease-out),
              transform var(--duration-150) var(--ease-out);
}

.dropdown.open .dropdown-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: var(--spacing-2) var(--spacing-4);
  color: var(--text-primary);
  text-decoration: none;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color var(--duration-150) var(--ease-in-out);
}

.dropdown-item:hover {
  background-color: var(--surface-hover);
}

.dropdown-item:first-child {
  border-top-left-radius: var(--radius-md);
  border-top-right-radius: var(--radius-md);
}

.dropdown-item:last-child {
  border-bottom-left-radius: var(--radius-md);
  border-bottom-right-radius: var(--radius-md);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border);
  margin: var(--spacing-1) 0;
}

/* ========== 通知组件 ========== */
.toast {
  position: fixed;
  top: var(--spacing-4);
  right: var(--spacing-4);
  min-width: 300px;
  background-color: var(--background);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-index-toast);
  transform: translateX(100%);
  transition: transform var(--duration-300) var(--ease-out);
}

.toast.show {
  transform: translateX(0);
}

.toast-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3) var(--spacing-4);
  border-bottom: 1px solid var(--border);
}

.toast-title {
  margin: 0;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.toast-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
}

.toast-body {
  padding: var(--spacing-4);
  color: var(--text-secondary);
}

/* 通知类型 */
.toast-success {
  border-left: 4px solid var(--success);
}

.toast-warning {
  border-left: 4px solid var(--warning);
}

.toast-error {
  border-left: 4px solid var(--error);
}

.toast-info {
  border-left: 4px solid var(--info);
}
