/**
 * API迁移验证工具专用样式
 * 扩展基础组件库，提供工具特定的UI样式
 */

/* ========== 工具主界面 ========== */
.api-migration-validator-modal .modal-content {
  max-width: 1000px;
  width: 95vw;
  max-height: 90vh;
}

.api-migration-validator-modal .modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.api-migration-validator-modal .header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

/* ========== 标签页内容 ========== */
.tab-pane {
  display: none;
}

.tab-pane.active {
  display: block;
}

/* ========== 工具栏样式 ========== */
.rules-toolbar,
.reports-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
}

.rules-toolbar .btn,
.reports-toolbar .btn {
  margin-right: var(--spacing-2);
}

.reports-count {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

/* ========== 规则列表样式 ========== */
.rules-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.rule-card {
  transition: box-shadow var(--duration-150) var(--ease-in-out);
}

.rule-card:hover {
  box-shadow: var(--shadow-md);
}

.rule-card .card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.rule-info {
  flex: 1;
  min-width: 0;
}

.rule-info .card-title {
  margin-bottom: var(--spacing-1);
}

.rule-description {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: var(--line-height-relaxed);
}

.rule-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.rule-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-3);
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.detail-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.detail-item code {
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
  background-color: var(--surface);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border);
  word-break: break-all;
}

/* ========== 报告列表样式 ========== */
.reports-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

/* ========== 简化的报告列表样式 ========== */
.report-item-simple {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-1);
  background-color: var(--background);
  transition: box-shadow var(--duration-150) var(--ease-in-out);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.report-item-simple:hover {
  box-shadow: var(--shadow-md);
}

.report-item-simple .api-name {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  border-left: 3px solid var(--primary);
  padding-left: var(--spacing-2);
  margin: 0;
  line-height: 1.2;
}

.report-item-simple .report-meta {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  margin-left: calc(var(--spacing-2) + 3px); /* 与API名称对齐 */
}

.report-item-simple .change-count {
  background-color: var(--primary);
  color: white;
  padding: 1px 6px;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
}

.report-item-simple .report-time {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
  white-space: nowrap;
}

.report-item-simple .btn {
  padding: 2px 8px;
  font-size: var(--font-size-xs);
  line-height: 1.2;
}

/* 保留原有的报告卡片样式以防需要 */
.report-card {
  transition: box-shadow var(--duration-150) var(--ease-in-out);
}

.report-card:hover {
  box-shadow: var(--shadow-md);
}

.report-card .card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: var(--spacing-4);
}

.report-info {
  flex: 1;
  min-width: 0;
}

.report-info .card-title {
  margin-bottom: var(--spacing-1);
}

.report-url {
  margin: 0;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-family: var(--font-mono);
  word-break: break-all;
}

.report-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  flex-shrink: 0;
}

.report-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-3);
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-2);
  background-color: var(--background);
  border-radius: var(--radius-sm);
}

.summary-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* ========== 规则编辑器样式 ========== */
.rule-editor-modal .modal-content {
  max-width: 800px;
  width: 90vw;
}

.rule-editor-container {
  min-height: 500px;
}

.form-section {
  margin-bottom: var(--spacing-6);
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-4);
}

.form-group-inline {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.form-group-inline .form-label {
  margin-bottom: 0;
  min-width: 120px;
}

/* 高级配置折叠面板 */
.advanced-config {
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.advanced-config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-3);
  background-color: var(--surface);
  cursor: pointer;
  transition: background-color var(--duration-150) var(--ease-in-out);
}

.advanced-config-header:hover {
  background-color: var(--surface-hover);
}

.advanced-config-content {
  padding: var(--spacing-4);
  border-top: 1px solid var(--border);
  display: none;
}

.advanced-config.expanded .advanced-config-content {
  display: block;
}

.advanced-config-toggle {
  background: none;
  border: none;
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: transform var(--duration-150) var(--ease-in-out);
}

.advanced-config.expanded .advanced-config-toggle {
  transform: rotate(180deg);
}

/* ========== 差异查看器样式 ========== */
.diff-viewer-modal .modal-content {
  max-width: 1200px;
  width: 95vw;
  max-height: 90vh;
}

.diff-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-4);
  padding: var(--spacing-3);
  background-color: var(--surface);
  border-radius: var(--radius-md);
}

.diff-summary {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.diff-meta {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.diff-header-info {
  margin-bottom: var(--spacing-4);
}

.diff-html-container {
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.diff-html-container h4 {
  margin: 0;
  padding: var(--spacing-3);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.jsondiff-output {
  max-height: 500px;
  overflow: auto;
  padding: var(--spacing-3);
  background-color: var(--background);
}

.jsondiff-output.compact {
  max-height: 200px;
  padding: var(--spacing-2);
}

.diff-actions-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4) var(--spacing-2);
  text-align: center;
  min-height: 200px;
}

.diff-notice {
  margin-bottom: var(--spacing-3);
  padding: var(--spacing-2);
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  color: var(--text-secondary);
}

.diff-notice p {
  margin: 0;
  font-size: var(--font-size-sm);
  line-height: 1.4;
}

.btn-large {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  border-radius: 6px;
  min-width: 200px;
}



/* jsondiffpatch HTML 输出样式 */
.jsondiff-output .jsondiffpatch-delta {
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-relaxed);
}

.jsondiff-output .jsondiffpatch-node {
  margin: var(--spacing-1) 0;
}

.jsondiff-output .jsondiffpatch-property-name {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.jsondiff-output .jsondiffpatch-added {
  background-color: var(--color-success-bg, #f0f9ff);
  color: var(--color-success);
}

.jsondiff-output .jsondiffpatch-deleted {
  background-color: var(--color-error-bg, #fef2f2);
  color: var(--color-error);
}

.jsondiff-output .jsondiffpatch-modified {
  background-color: var(--color-warning-bg, #fffbeb);
  color: var(--color-warning);
}

.jsondiff-output .jsondiffpatch-unchanged {
  color: var(--text-secondary);
}

.side-by-side-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-2);
  height: 400px;
}

.response-panel {
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.response-panel h4 {
  margin: 0;
  padding: var(--spacing-3);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.response-meta {
  padding: var(--spacing-2);
  background-color: var(--background);
  border-bottom: 1px solid var(--border);
}

.response-meta.compact {
  padding: var(--spacing-1) var(--spacing-2);
}

.meta-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-1);
}

.status-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

.time-badge {
  display: inline-block;
  padding: 2px 6px;
  background-color: var(--surface);
  border-radius: 3px;
  font-size: 11px;
  color: var(--text-secondary);
  line-height: 1;
}

.url-display {
  font-size: 11px;
  color: var(--text-secondary);
  word-break: break-all;
  line-height: 1.2;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-1);
  font-size: var(--font-size-xs);
}

.meta-item:last-child {
  margin-bottom: 0;
}

.meta-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  min-width: 80px;
}

.meta-value {
  color: var(--text-primary);
  word-break: break-all;
}

.status-success {
  background-color: var(--color-success-bg, #f0f9ff);
  color: var(--color-success);
}

.status-error {
  background-color: var(--color-error-bg, #fef2f2);
  color: var(--color-error);
}

.error-message {
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--color-error-bg, #fef2f2);
  color: var(--color-error);
  border-bottom: 1px solid var(--border);
  font-size: 11px;
  line-height: 1.3;
}

.response-body-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.response-body-container h5 {
  margin: 0;
  padding: var(--spacing-1) var(--spacing-2);
  background-color: var(--surface);
  border-bottom: 1px solid var(--border);
  font-size: 11px;
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

.response-body {
  margin: 0;
  padding: var(--spacing-2);
  overflow: auto;
  flex: 1;
  font-family: var(--font-mono);
  font-size: 11px;
  line-height: 1.4;
  background-color: var(--background);
}

.response-panel pre {
  margin: 0;
  padding: var(--spacing-3);
  overflow: auto;
  height: calc(100% - 48px);
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
  line-height: var(--line-height-relaxed);
}

.raw-data-container h4 {
  margin: var(--spacing-4) 0 var(--spacing-2) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.raw-data-container pre {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  overflow: auto;
  max-height: 300px;
  font-family: var(--font-mono);
  font-size: var(--font-size-xs);
}

/* ========== 空状态样式 ========== */
.empty-state {
  text-align: center;
  padding: var(--spacing-8);
  color: var(--text-secondary);
}

.empty-state p {
  margin: var(--spacing-2) 0;
}

.empty-state .text-muted {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
}

/* ========== 依赖指南样式 ========== */
.dependency-guide {
  text-align: center;
}

.dependency-guide h4 {
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

.dependency-guide p {
  margin-bottom: var(--spacing-3);
  color: var(--text-secondary);
}

.code-block {
  background-color: var(--surface);
  border: 1px solid var(--border);
  border-radius: var(--radius-md);
  padding: var(--spacing-3);
  margin: var(--spacing-4) 0;
}

.code-block code {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
  .api-migration-validator-modal .modal-content {
    width: 100vw;
    height: 100vh;
    max-width: none;
    max-height: none;
    border-radius: 0;
  }

  .rule-details,
  .report-summary {
    grid-template-columns: 1fr;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .side-by-side-container {
    grid-template-columns: 1fr;
    height: auto;
    gap: var(--spacing-1);
  }

  .response-panel {
    height: 250px;
  }

  .diff-viewer-modal .modal-content {
    max-width: 95vw;
    width: 95vw;
    max-height: 85vh;
  }

  .response-meta.compact {
    padding: var(--spacing-1);
  }

  .meta-row {
    flex-wrap: wrap;
    gap: var(--spacing-1);
  }

  .url-display {
    font-size: 10px;
  }
}

/* 扩展环境特殊优化 */
@media (max-width: 600px) {
  .diff-viewer-modal .modal-content {
    max-width: 98vw;
    width: 98vw;
    max-height: 90vh;
  }

  .side-by-side-container {
    height: auto;
    max-height: 300px;
  }

  .response-panel {
    height: 200px;
  }

  .response-body {
    font-size: 10px;
    line-height: 1.3;
    padding: var(--spacing-1);
  }

  .jsondiff-output {
    max-height: 300px;
    padding: var(--spacing-2);
  }

  .tabs-list {
    font-size: var(--font-size-xs);
  }

  .modal-header h3 {
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 768px) {
  .rules-toolbar,
  .reports-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .rule-card .card-header,
  .report-card .card-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
  }

  .rule-actions,
  .report-actions {
    justify-content: flex-start;
  }

  /* 简化报告列表的响应式样式 */
  .report-item-simple .report-meta {
    flex-wrap: wrap;
    gap: var(--spacing-2);
    margin-left: 0;
  }

  .report-item-simple .api-name {
    font-size: var(--font-size-sm);
    padding-left: var(--spacing-1);
  }

  .report-item-simple .btn {
    padding: 1px 6px;
    font-size: 10px;
  }
}
