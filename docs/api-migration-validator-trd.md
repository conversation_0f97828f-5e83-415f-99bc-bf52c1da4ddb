# API迁移验证工具 - 技术需求文档 (TRD)

## 1. 技术架构概述

### 1.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    fwyy-tools Framework                     │
├─────────────────────────────────────────────────────────────┤
│  API Migration Validator Tool                              │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   UI Layer      │ │  Logic Layer    │ │  Data Layer     ││
│  │                 │ │                 │ │                 ││
│  │ • Rule Editor   │ │ • Interceptor   │ │ • Local Storage ││
│  │ • Diff Viewer   │ │ • Transformer   │ │ • Config Mgmt   ││
│  │ • Report List   │ │ • Comparator    │ │ • Report Store  ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
├─────────────────────────────────────────────────────────────┤
│  External Dependencies                                     │
│  • @mswjs/interceptors  • jsondiffpatch                    │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心组件
- **ApiMigrationValidatorTool**: 主工具类，继承BaseTool
- **RuleManager**: 规则管理器
- **NetworkInterceptor**: 网络拦截器
- **RequestTransformer**: 请求转换器
- **DiffComparator**: 差异对比器
- **ReportGenerator**: 报告生成器

## 2. 核心技术选型

### 2.1 网络拦截技术
**选择**: @mswjs/interceptors v0.29.0

**技术优势**:
- 低级别网络拦截，支持fetch和XMLHttpRequest
- 浏览器扩展兼容性好
- 活跃维护，API稳定
- 支持请求/响应事件处理

**实现方案**:
```typescript
import { BatchInterceptor } from '@mswjs/interceptors'
import { FetchInterceptor } from '@mswjs/interceptors/fetch'
import { XMLHttpRequestInterceptor } from '@mswjs/interceptors/XMLHttpRequest'

const interceptor = new BatchInterceptor({
  name: 'api-migration-validator',
  interceptors: [
    new FetchInterceptor(),
    new XMLHttpRequestInterceptor(),
  ],
})
```

### 2.2 JSON差异对比技术
**选择**: jsondiffpatch v0.6.0

**技术优势**:
- 高性能的JSON对比算法
- 支持可视化HTML输出
- 丰富的配置选项
- 支持数组移动检测

**实现方案**:
```typescript
import * as jsondiffpatch from 'jsondiffpatch'

const differ = jsondiffpatch.create({
  objectHash: (obj) => obj.id || obj._id || obj.name,
  arrays: { detectMove: true },
  textDiff: { minLength: 60 }
})
```

### 2.3 数据存储技术
**选择**: Chrome Storage API + IndexedDB

**存储策略**:
- 规则配置: chrome.storage.local (同步性好)
- 对比报告: IndexedDB (支持大数据量)
- 用户设置: chrome.storage.sync (跨设备同步)

## 3. 数据模型设计

### 3.1 拦截规则模型
```typescript
interface InterceptRule {
  id: string                    // 唯一标识
  name: string                  // 规则名称
  description?: string          // 规则描述
  enabled: boolean             // 启用状态
  priority: number             // 优先级 (0-999)
  createdAt: number            // 创建时间
  updatedAt: number            // 更新时间
  
  // 匹配条件
  conditions: {
    urlPattern: string         // URL匹配模式
    urlMatchType: 'exact' | 'contains' | 'startsWith' | 'endsWith' | 'regex'
    methods?: string[]         // HTTP方法过滤
    headers?: Record<string, string>  // 请求头条件
  }
  
  // 转换规则
  transformation: {
    newUrl: string             // 新接口URL
    paramMapping?: Record<string, string>      // 参数映射
    headerMapping?: Record<string, string>     // 请求头映射
    preserveOriginalParams?: boolean           // 保留原始参数
  }
  
  // 执行模式
  mode: 'redirect' | 'parallel'
  
  // 差异配置
  diffConfig?: {
    ignoreFields?: string[]    // 忽略字段
    caseSensitive?: boolean    // 大小写敏感
    arrayOrderSensitive?: boolean  // 数组顺序敏感
    numericTolerance?: number  // 数值容差
  }
}
```

### 3.2 对比报告模型
```typescript
interface DiffReport {
  id: string                   // 报告ID
  ruleId: string              // 关联规则ID
  ruleName: string            // 规则名称
  timestamp: number           // 生成时间
  
  // 请求信息
  request: {
    url: string
    method: string
    headers: Record<string, string>
    body?: any
  }
  
  // 响应信息
  responses: {
    old: ApiResponse
    new: ApiResponse
  }
  
  // 差异信息
  diff: {
    delta: any                 // jsondiffpatch差异对象
    changeCount: number        // 变更数量
    severity: 'low' | 'medium' | 'high'  // 严重程度
  }
  
  // 可视化数据
  visualizations: {
    html: string              // HTML差异视图
    summary: string           // 差异摘要
  }
}

interface ApiResponse {
  status: number
  statusText: string
  headers: Record<string, string>
  body: any
  responseTime: number
  error?: string
}
```

## 4. 核心算法设计

### 4.1 规则匹配算法
```typescript
class RuleMatcher {
  findMatchingRule(url: string, method: string, headers: Record<string, string>): InterceptRule | null {
    // 1. 按优先级排序
    const sortedRules = this.rules
      .filter(rule => rule.enabled)
      .sort((a, b) => b.priority - a.priority)
    
    // 2. 逐一匹配
    for (const rule of sortedRules) {
      if (this.isRuleMatched(rule, url, method, headers)) {
        return rule
      }
    }
    
    return null
  }
  
  private isRuleMatched(rule: InterceptRule, url: string, method: string, headers: Record<string, string>): boolean {
    // URL匹配 + HTTP方法匹配 + 请求头匹配
    return this.isUrlMatched(rule.conditions, url) &&
           this.isMethodMatched(rule.conditions, method) &&
           this.isHeaderMatched(rule.conditions, headers)
  }
}
```

### 4.2 请求转换算法
```typescript
class RequestTransformer {
  transform(originalRequest: any, rule: InterceptRule): any {
    const transformed = { ...originalRequest }
    
    // 1. URL转换
    transformed.url = this.transformUrl(originalRequest.url, rule.transformation.newUrl)
    
    // 2. 参数映射
    if (rule.transformation.paramMapping) {
      transformed.params = this.mapParameters(originalRequest.params, rule.transformation.paramMapping)
    }
    
    // 3. 请求头映射
    if (rule.transformation.headerMapping) {
      transformed.headers = this.mapHeaders(originalRequest.headers, rule.transformation.headerMapping)
    }
    
    return transformed
  }
}
```

### 4.3 差异对比算法
```typescript
class DiffComparator {
  compare(oldResponse: any, newResponse: any, config?: DiffConfig): DiffResult {
    // 1. 数据预处理
    const processedOld = this.preprocessData(oldResponse, config)
    const processedNew = this.preprocessData(newResponse, config)
    
    // 2. 执行差异对比
    const delta = jsondiffpatch.diff(processedOld, processedNew)
    
    // 3. 分析差异结果
    const changeCount = this.countChanges(delta)
    const severity = this.determineSeverity(changeCount, oldResponse, newResponse)
    
    // 4. 生成可视化
    const html = jsondiffpatch.formatters.html.format(delta, processedOld)
    
    return { delta, changeCount, severity, html }
  }
}
```

## 5. 性能优化策略

### 5.1 拦截性能优化
- **规则缓存**: 将编译后的正则表达式缓存
- **快速匹配**: 优先使用字符串匹配，减少正则表达式使用
- **异步处理**: 拦截逻辑异步执行，避免阻塞主线程

### 5.2 对比性能优化
- **增量对比**: 对于大型JSON，使用增量对比算法
- **并行处理**: 多个对比任务并行执行
- **结果缓存**: 缓存相同请求的对比结果

### 5.3 内存优化
- **数据清理**: 定期清理过期的报告数据
- **懒加载**: 报告详情按需加载
- **压缩存储**: 使用压缩算法存储大型数据

## 6. 安全设计

### 6.1 数据安全
- **本地存储**: 敏感数据仅存储在本地
- **数据脱敏**: 自动检测并脱敏敏感信息
- **加密存储**: 可选的配置数据加密

### 6.2 网络安全
- **HTTPS优先**: 优先使用HTTPS协议
- **CSP兼容**: 兼容Content Security Policy
- **权限最小化**: 仅请求必要的浏览器权限

## 7. 错误处理策略

### 7.1 拦截错误处理
```typescript
try {
  await this.handleRequest(params)
} catch (error) {
  // 记录错误日志
  console.error('Request handling failed:', error)
  
  // 用户通知
  await this.showNotification('错误', '请求处理失败')
  
  // 降级处理：允许原始请求继续
  return this.passthrough(params)
}
```

### 7.2 对比错误处理
- **格式错误**: 自动检测并处理非JSON响应
- **超时处理**: 设置合理的请求超时时间
- **网络错误**: 优雅处理网络连接问题

## 8. 测试策略

### 8.1 单元测试
- 规则匹配逻辑测试
- 请求转换逻辑测试
- 差异对比算法测试

### 8.2 集成测试
- 端到端拦截流程测试
- 多规则优先级测试
- 并发请求处理测试

### 8.3 性能测试
- 大量规则匹配性能测试
- 大型JSON对比性能测试
- 内存泄漏检测

## 9. 部署和维护

### 9.1 版本管理
- 语义化版本控制
- 向后兼容性保证
- 数据迁移策略

### 9.2 监控和日志
- 性能指标监控
- 错误日志收集
- 用户行为分析

### 9.3 更新机制
- 自动更新检测
- 增量更新支持
- 回滚机制
