# API迁移验证工具 - 实现文档

## 1. 项目概述

API迁移验证工具是一个基于fwyy-tools框架开发的浏览器扩展工具，用于自动化验证API接口迁移的兼容性和一致性。工具支持两种验证模式：重定向替换和并行对比，帮助开发团队快速发现新旧API之间的差异。

## 2. 技术栈

- **框架**: fwyy-tools (WXT-based browser extension framework)
- **语言**: TypeScript
- **网络拦截**: @mswjs/interceptors v0.29.0
- **差异对比**: jsondiffpatch v0.6.0
- **样式**: CSS3 + CSS Variables
- **存储**: Chrome Storage API + IndexedDB

## 3. 文件结构

```
tools/
├── api-migration-validator.ts    # 主工具实现文件 (2170行)
styles/
├── api-migration-validator.css   # 专用样式文件 (300+行)
docs/
├── api-migration-validator-prd.md    # 产品需求文档
├── api-migration-validator-trd.md    # 技术需求文档
└── api-migration-validator-implementation.md  # 本文档
```

## 4. 核心功能实现

### 4.1 主工具类 (ApiMigrationValidatorTool)

继承自BaseTool，实现了完整的工具生命周期：

```typescript
export class ApiMigrationValidatorTool extends BaseTool {
  id = 'api-migration-validator'
  name = 'API迁移验证工具'
  version = '1.0.0'
  
  dependencies = [
    { id: '@mswjs/interceptors', version: '^0.29.0', external: true },
    { id: 'jsondiffpatch', version: '^0.6.0', external: true }
  ]
}
```

### 4.2 规则管理系统

#### 数据模型
- **InterceptRule**: 拦截规则接口，包含匹配条件、转换规则、执行模式等
- **DiffReport**: 对比报告接口，包含请求信息、响应数据、差异分析等

#### 核心方法
- `showRuleEditor()`: 显示规则编辑器
- `createRule()`: 创建新规则
- `updateRule()`: 更新现有规则
- `deleteRule()`: 删除规则
- `toggleRule()`: 切换规则启用状态

### 4.3 网络拦截引擎

#### 拦截逻辑
```typescript
private async handleRequest(params: any): Promise<void> {
  // 1. 查找匹配的规则
  const matchedRule = this.findMatchingRule(url, method, headers)
  
  // 2. 根据模式执行不同策略
  if (matchedRule.mode === 'redirect') {
    await this.redirectRequest(params, matchedRule)
  } else {
    await this.parallelCompare(params, matchedRule)
  }
}
```

#### 匹配算法
- 支持5种URL匹配模式：精确、包含、开头、结尾、正则表达式
- HTTP方法过滤
- 请求头条件匹配
- 优先级排序

### 4.4 请求转换系统

#### 转换功能
- URL重写和变量替换
- 参数名映射 (oldParam → newParam)
- 请求头映射
- 原始参数保留策略

#### 实现示例
```typescript
private transformRequest(originalRequest: any, rule: InterceptRule): any {
  const transformed = { ...originalRequest }
  
  // URL转换
  transformed.url = transformation.newUrl
  
  // 参数映射
  if (transformation.paramMapping) {
    const newParams = {}
    for (const [oldKey, newKey] of Object.entries(transformation.paramMapping)) {
      if (originalRequest.params[oldKey] !== undefined) {
        newParams[newKey] = originalRequest.params[oldKey]
      }
    }
    transformed.params = newParams
  }
  
  return transformed
}
```

### 4.5 差异对比引擎

#### 对比流程
1. 并行调用新旧API接口
2. 使用jsondiffpatch进行深度对比
3. 计算变更数量和严重程度
4. 生成HTML可视化差异
5. 保存对比报告

#### 核心算法
```typescript
private async generateDiffReport(request: any, rule: InterceptRule, oldResponse: any, newResponse: any): Promise<DiffReport> {
  // 使用jsondiffpatch进行对比
  const delta = jsondiffpatch.diff(oldResponse.body, newResponse.body)
  
  // 计算变更数量
  const changeCount = this.countChanges(delta)
  
  // 确定严重程度
  const severity = this.determineSeverity(changeCount, oldResponse, newResponse)
  
  // 生成HTML可视化
  const html = jsondiffpatch.formatters.html.format(delta, oldResponse.body)
  
  return { id, ruleId, ruleName, timestamp, request, responses, diff, visualizations }
}
```

## 5. 用户界面实现

### 5.1 主界面结构
- **标签页设计**: 规则管理 | 对比报告 | 设置配置
- **响应式布局**: 支持桌面和移动端
- **模态框系统**: 规则编辑器、差异查看器、测试界面

### 5.2 规则编辑器
四标签页设计：
1. **基础信息**: 名称、描述、优先级、执行模式
2. **匹配条件**: URL模式、HTTP方法、请求头条件
3. **转换规则**: 新URL、参数映射、请求头映射
4. **对比设置**: 忽略字段、大小写敏感、数值容差

### 5.3 差异查看器
三视图模式：
1. **可视化差异**: jsondiffpatch HTML格式化输出
2. **并排对比**: 新旧响应数据并排显示
3. **原始数据**: 完整的请求和响应信息

## 6. 数据持久化

### 6.1 存储策略
- **规则配置**: Chrome Storage Local API
- **对比报告**: Chrome Storage Local API (考虑未来迁移到IndexedDB)
- **用户设置**: Chrome Storage Sync API

### 6.2 数据格式
```typescript
// 存储键值
const STORAGE_KEYS = {
  RULES: 'api_migration_rules',
  REPORTS: 'api_migration_reports',
  SETTINGS: 'api_migration_settings'
}
```

### 6.3 导入导出
- 支持JSON格式的规则配置导入导出
- 版本化数据格式，确保向后兼容
- 自动数据验证和错误处理

## 7. 性能优化

### 7.1 拦截性能
- 规则匹配算法优化，优先级排序
- 正则表达式缓存
- 异步处理避免阻塞

### 7.2 UI性能
- 虚拟滚动处理大量报告
- 懒加载报告详情
- 防抖处理用户输入

### 7.3 内存管理
- 定期清理过期报告
- 限制报告数量上限
- 及时释放DOM引用

## 8. 错误处理

### 8.1 拦截错误
- 网络请求失败处理
- 规则匹配异常处理
- 降级策略：允许原始请求继续

### 8.2 UI错误
- 表单验证错误提示
- 网络错误用户通知
- 优雅的错误恢复

### 8.3 数据错误
- JSON解析错误处理
- 存储操作失败处理
- 数据格式验证

## 9. 测试验证

### 9.1 功能测试
- 规则创建、编辑、删除
- 网络请求拦截和转换
- 差异对比和报告生成
- 导入导出功能

### 9.2 兼容性测试
- 多浏览器兼容性
- 不同网站和API类型
- 各种Content-Type格式

### 9.3 性能测试
- 大量规则匹配性能
- 大型JSON对比性能
- 并发请求处理能力

## 10. 部署说明

### 10.1 依赖安装
工具依赖两个外部库，需要在使用前确保已安装：
```bash
npm install @mswjs/interceptors@^0.29.0
npm install jsondiffpatch@^0.6.0
```

### 10.2 集成步骤
1. 将工具文件添加到fwyy-tools项目
2. 安装必要的依赖库
3. 在工具注册表中注册工具
4. 编译和打包扩展

### 10.3 使用流程
1. 打开工具界面
2. 创建拦截规则
3. 启用网络拦截
4. 访问目标页面触发API调用
5. 查看对比报告和差异分析

## 11. 未来扩展

### 11.1 功能扩展
- 支持GraphQL API对比
- 添加性能指标对比
- 集成CI/CD流水线
- 团队协作功能

### 11.2 技术优化
- 使用Web Workers提升性能
- 支持更多数据格式
- 增强安全性和隐私保护
- 云端配置同步

## 12. 总结

API迁移验证工具已完成完整的功能实现，包括：
- ✅ 完整的规则管理系统
- ✅ 强大的网络拦截引擎
- ✅ 智能的差异对比算法
- ✅ 直观的用户界面
- ✅ 可靠的数据持久化
- ✅ 完善的错误处理

工具现在可以投入实际使用，为API迁移验证提供强有力的自动化支持。
