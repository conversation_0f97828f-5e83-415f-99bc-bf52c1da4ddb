/**
 * 规则匹配引擎
 * 负责检查请求是否匹配拦截规则
 */

import { InterceptRule, MatchConditions } from '../types/interfaces';

export class RuleMatcher {
  private rules: InterceptRule[] = [];

  /**
   * 设置规则列表
   */
  setRules(rules: InterceptRule[]): void {
    this.rules = rules
      .filter(rule => rule.enabled)
      .sort((a, b) => b.priority - a.priority); // 按优先级降序排列
  }

  /**
   * 查找匹配的规则
   */
  findMatchingRule(url: string, method: string, headers?: Record<string, string>): InterceptRule | null {
    for (const rule of this.rules) {
      if (this.isRuleMatched(rule, url, method, headers)) {
        return rule;
      }
    }
    return null;
  }

  /**
   * 检查规则是否匹配
   */
  private isRuleMatched(
    rule: InterceptRule, 
    url: string, 
    method: string, 
    headers?: Record<string, string>
  ): boolean {
    const { conditions } = rule;

    // URL匹配检查
    if (!this.isUrlMatched(conditions, url)) {
      return false;
    }

    // HTTP方法检查
    if (!this.isMethodMatched(conditions, method)) {
      return false;
    }

    // 请求头检查
    if (!this.areHeadersMatched(conditions, headers)) {
      return false;
    }

    // 查询参数检查
    if (!this.areQueryParamsMatched(conditions, url)) {
      return false;
    }

    return true;
  }

  /**
   * URL匹配检查
   */
  private isUrlMatched(conditions: MatchConditions, url: string): boolean {
    const { urlPattern, urlMatchType } = conditions;

    if (typeof urlPattern === 'string') {
      switch (urlMatchType) {
        case 'exact':
          return url === urlPattern;
        case 'contains':
          return url.includes(urlPattern);
        case 'startsWith':
          return url.startsWith(urlPattern);
        case 'endsWith':
          return url.endsWith(urlPattern);
        case 'regex':
          try {
            const regex = new RegExp(urlPattern);
            return regex.test(url);
          } catch (error) {
            console.warn('Invalid regex pattern:', urlPattern, error);
            return false;
          }
        default:
          return url.includes(urlPattern);
      }
    } else if (urlPattern instanceof RegExp) {
      return urlPattern.test(url);
    }

    return false;
  }

  /**
   * HTTP方法匹配检查
   */
  private isMethodMatched(conditions: MatchConditions, method: string): boolean {
    if (!conditions.methods || conditions.methods.length === 0) {
      return true; // 未指定方法则匹配所有方法
    }
    return conditions.methods.includes(method.toUpperCase());
  }

  /**
   * 请求头匹配检查
   */
  private areHeadersMatched(conditions: MatchConditions, headers?: Record<string, string>): boolean {
    if (!conditions.headers) {
      return true; // 未指定请求头条件则通过
    }

    if (!headers) {
      return false; // 有请求头条件但请求无请求头
    }

    for (const [key, expectedValue] of Object.entries(conditions.headers)) {
      const actualValue = headers[key] || headers[key.toLowerCase()];
      
      if (!actualValue) {
        return false; // 缺少必需的请求头
      }

      if (typeof expectedValue === 'string') {
        if (actualValue !== expectedValue) {
          return false;
        }
      } else if (expectedValue instanceof RegExp) {
        if (!expectedValue.test(actualValue)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * 查询参数匹配检查
   */
  private areQueryParamsMatched(conditions: MatchConditions, url: string): boolean {
    if (!conditions.queryParams) {
      return true; // 未指定查询参数条件则通过
    }

    try {
      const urlObj = new URL(url);
      const params = urlObj.searchParams;

      for (const [key, expectedValue] of Object.entries(conditions.queryParams)) {
        const actualValue = params.get(key);
        
        if (!actualValue) {
          return false; // 缺少必需的查询参数
        }

        if (typeof expectedValue === 'string') {
          if (actualValue !== expectedValue) {
            return false;
          }
        } else if (expectedValue instanceof RegExp) {
          if (!expectedValue.test(actualValue)) {
            return false;
          }
        }
      }

      return true;
    } catch (error) {
      console.warn('Failed to parse URL for query params:', url, error);
      return false;
    }
  }

  /**
   * 获取所有启用的规则
   */
  getEnabledRules(): InterceptRule[] {
    return this.rules;
  }

  /**
   * 获取规则统计信息
   */
  getStats(): { total: number; enabled: number; disabled: number } {
    const total = this.rules.length;
    const enabled = this.rules.filter(rule => rule.enabled).length;
    const disabled = total - enabled;

    return { total, enabled, disabled };
  }

  /**
   * 验证规则配置
   */
  validateRule(rule: InterceptRule): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 基础字段验证
    if (!rule.name || rule.name.trim() === '') {
      errors.push('规则名称不能为空');
    }

    if (typeof rule.priority !== 'number' || rule.priority < 0) {
      errors.push('优先级必须是非负数');
    }

    // URL模式验证
    if (!rule.conditions.urlPattern) {
      errors.push('URL匹配模式不能为空');
    } else if (rule.conditions.urlMatchType === 'regex' && typeof rule.conditions.urlPattern === 'string') {
      try {
        new RegExp(rule.conditions.urlPattern);
      } catch (error) {
        errors.push('URL正则表达式格式无效');
      }
    }

    // 转换规则验证
    if (!rule.transformation.newUrl || rule.transformation.newUrl.trim() === '') {
      errors.push('新URL不能为空');
    }

    // 请求体转换函数验证
    if (rule.transformation.bodyTransform) {
      try {
        // 简单的语法检查
        new Function('body', rule.transformation.bodyTransform);
      } catch (error) {
        errors.push('请求体转换函数语法错误');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
