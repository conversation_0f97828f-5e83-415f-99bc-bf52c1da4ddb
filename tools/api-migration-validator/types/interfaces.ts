/**
 * API迁移验证工具 - 类型定义
 * 定义所有核心接口和类型
 */

/**
 * 拦截规则接口
 */
export interface InterceptRule {
  /** 规则唯一标识 */
  id: string;
  /** 规则名称 */
  name: string;
  /** 规则描述 */
  description?: string;
  /** 是否启用 */
  enabled: boolean;
  /** 优先级（数字越大优先级越高） */
  priority: number;
  /** 创建时间 */
  createdAt: number;
  /** 更新时间 */
  updatedAt: number;

  /** 匹配条件 */
  conditions: MatchConditions;
  /** 转换规则 */
  transformation: TransformationRules;
  /** 执行模式 */
  mode: ExecutionMode;
  /** 对比配置 */
  diffConfig?: DiffConfig;
}

/**
 * 匹配条件
 */
export interface MatchConditions {
  /** URL匹配模式 */
  urlPattern: string | RegExp;
  /** URL匹配类型 */
  urlMatchType: 'contains' | 'startsWith' | 'endsWith' | 'regex' | 'exact';
  /** HTTP方法过滤 */
  methods?: string[];
  /** 请求头条件 */
  headers?: Record<string, string | RegExp>;
  /** 查询参数条件 */
  queryParams?: Record<string, string | RegExp>;
}

/**
 * 转换规则
 */
export interface TransformationRules {
  /** 新的URL模板 */
  newUrl: string;
  /** 参数映射规则 */
  paramMapping?: Record<string, string>;
  /** 请求头映射 */
  headerMapping?: Record<string, string>;
  /** 查询参数映射 */
  queryMapping?: Record<string, string>;
  /** 请求体转换函数（字符串形式，运行时eval） */
  bodyTransform?: string;
  /** 是否保留原始参数 */
  preserveOriginalParams?: boolean;
}

/**
 * 执行模式
 */
export type ExecutionMode = 'redirect' | 'parallel';

/**
 * 对比配置
 */
export interface DiffConfig {
  /** 忽略的字段路径 */
  ignoreFields?: string[];
  /** 是否大小写敏感 */
  caseSensitive?: boolean;
  /** 数组顺序是否敏感 */
  arrayOrderSensitive?: boolean;
  /** 数值精度容差 */
  numericTolerance?: number;
  /** 自定义对比器 */
  customComparers?: Record<string, string>;
}

/**
 * 对比报告
 */
export interface DiffReport {
  /** 报告ID */
  id: string;
  /** 规则ID */
  ruleId: string;
  /** 规则名称 */
  ruleName: string;
  /** 生成时间 */
  timestamp: number;
  
  /** 请求信息 */
  request: {
    url: string;
    method: string;
    headers: Record<string, string>;
    body?: any;
  };
  
  /** 响应信息 */
  responses: {
    old: ResponseInfo;
    new: ResponseInfo;
  };
  
  /** 差异信息 */
  diff: {
    /** jsondiffpatch原生delta */
    delta: any;
    /** 是否有差异 */
    hasChanges: boolean;
    /** 变更数量 */
    changeCount: number;
    /** 严重程度 */
    severity: 'none' | 'minor' | 'major' | 'critical';
  };
  
  /** 可视化数据 */
  visualizations: {
    html: string;
    console: string;
    annotated: string;
  };
  
  /** 性能信息 */
  performance: {
    oldResponseTime: number;
    newResponseTime: number;
    diffTime: number;
  };
}

/**
 * 响应信息
 */
export interface ResponseInfo {
  /** 状态码 */
  status: number;
  /** 状态文本 */
  statusText: string;
  /** 响应头 */
  headers: Record<string, string>;
  /** 响应体 */
  body: any;
  /** 响应时间（毫秒） */
  responseTime: number;
  /** 是否成功 */
  success: boolean;
  /** 错误信息 */
  error?: string;
}

/**
 * 工具配置
 */
export interface ToolConfig {
  /** 是否启用拦截 */
  enabled: boolean;
  /** 全局忽略模式 */
  globalIgnorePatterns: string[];
  /** 最大报告数量 */
  maxReports: number;
  /** 自动清理时间（天） */
  autoCleanupDays: number;
  /** 通知设置 */
  notifications: {
    showSuccess: boolean;
    showWarnings: boolean;
    showErrors: boolean;
  };
  /** UI设置 */
  ui: {
    defaultDiffView: 'side-by-side' | 'unified';
    showLineNumbers: boolean;
    highlightChanges: boolean;
  };
}

/**
 * 规则验证结果
 */
export interface RuleValidationResult {
  /** 是否有效 */
  valid: boolean;
  /** 错误信息 */
  errors: string[];
  /** 警告信息 */
  warnings: string[];
}

/**
 * 拦截器状态
 */
export interface InterceptorStatus {
  /** 是否运行中 */
  running: boolean;
  /** 拦截的请求数量 */
  interceptedCount: number;
  /** 生成的报告数量 */
  reportCount: number;
  /** 启动时间 */
  startTime?: number;
  /** 最后活动时间 */
  lastActivity?: number;
}

/**
 * 事件类型
 */
export type EventType = 
  | 'rule:created'
  | 'rule:updated' 
  | 'rule:deleted'
  | 'rule:enabled'
  | 'rule:disabled'
  | 'request:intercepted'
  | 'report:generated'
  | 'interceptor:started'
  | 'interceptor:stopped';

/**
 * 事件数据
 */
export interface EventData {
  type: EventType;
  timestamp: number;
  data: any;
}

/**
 * 导入导出数据格式
 */
export interface ExportData {
  version: string;
  exportTime: number;
  rules: InterceptRule[];
  config: ToolConfig;
}
